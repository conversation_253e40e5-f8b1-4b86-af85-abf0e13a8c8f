# 问卷统计分析模块

## 概述

本模块实现了教师评价问卷系统的统计分析功能，提供学校维度和教师维度的多层次数据分析，包含趋势分析、排名统计、评分分布、关键词云等功能。

## 功能特性

### 🏫 学校维度统计
- ✅ 学校整体平均分计算（基于所有家长的学校评分）
- ✅ 按月份筛选统计结果
- ✅ 生成趋势对比图表数据
- ✅ 获取教师评分排名（按平均分降序）
- ✅ 完成率和响应数量统计

### 👨‍🏫 教师维度统计
- ✅ 教师个人平均分计算
- ✅ 评分分布分析（90-100分、80-89分等占比）
- ✅ 家长评价描述的关键词云数据
- ✅ 推荐率统计
- ✅ 细分评分统计（教学质量、教学态度等）

### 📊 高级分析功能
- ✅ 趋势分析（支持学校和教师两种模式）
- ✅ 教师排名（支持多种排序方式）
- ✅ 原生SQL查询优化性能
- ✅ 灵活的筛选条件（学校ID、月份、科目、部门等）

## 数据模型

### 学校统计数据结构

```typescript
interface ISchoolStatistics {
  sso_school_code: string;           // 学校ID
  sso_school_name?: string;        // 学校名称
  month?: string;                  // 统计月份
  total_responses: number;         // 总响应数
  completed_responses: number;     // 完成响应数
  completion_rate: number;         // 完成率(%)
  school_average_score: number;    // 学校平均分
  teacher_average_score: number;   // 教师平均分
  total_teachers_evaluated: number; // 被评价教师总数
  response_trend?: ITrendData[];   // 响应趋势数据
  teacher_ranking?: ITeacherRanking[]; // 教师排名
}
```

### 教师统计数据结构

```typescript
interface ITeacherStatistics {
  sso_teacher_id: string;          // 教师ID
  sso_teacher_name?: string;       // 教师姓名
  sso_teacher_subject?: string;    // 教师科目
  sso_teacher_department?: string; // 教师部门
  month?: string;                  // 统计月份
  total_evaluations: number;       // 总评价数
  average_score: number;           // 平均分
  recommendation_rate: number;     // 推荐率(%)
  score_distribution?: IScoreDistribution[]; // 评分分布
  keyword_cloud?: IKeywordData[];  // 关键词云
  evaluation_trend?: ITrendData[]; // 评价趋势
  detailed_scores?: IDetailedScores; // 细分评分
}
```

## API 接口

### 1. 获取学校维度统计

**GET** `/api/statistics/school`

**查询参数：**
- `sso_school_code`: 学校ID（必填）
- `month`: 月份 YYYY-MM（可选）
- `start_month`: 开始月份（可选）
- `end_month`: 结束月份（可选）
- `include_trend`: 是否包含趋势数据（可选）
- `include_teacher_ranking`: 是否包含教师排名（可选）

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取学校统计数据成功",
  "data": {
    "sso_school_code": "school_001",
    "sso_school_name": "示例小学",
    "month": "2024-01",
    "total_responses": 150,
    "completed_responses": 142,
    "completion_rate": 94.67,
    "school_average_score": 87.5,
    "teacher_average_score": 89.2,
    "total_teachers_evaluated": 25,
    "response_trend": [...],
    "teacher_ranking": [...]
  }
}
```

### 2. 获取教师维度统计

**GET** `/api/statistics/teacher`

**查询参数：**
- `sso_teacher_id`: 教师ID（必填）
- `sso_school_code`: 学校ID（可选）
- `month`: 月份 YYYY-MM（可选）
- `start_month`: 开始月份（可选）
- `end_month`: 结束月份（可选）
- `include_distribution`: 是否包含评分分布（可选）
- `include_keywords`: 是否包含关键词云（可选）
- `include_trend`: 是否包含趋势数据（可选）

### 3. 获取教师排名

**GET** `/api/statistics/teacher-ranking`

**查询参数：**
- `sso_school_code`: 学校ID（必填）
- `month`: 月份（可选）
- `subject`: 科目筛选（可选）
- `department`: 部门筛选（可选）
- `page`: 页码，默认1
- `limit`: 每页数量，默认20
- `sort_by`: 排序字段（average_score/evaluation_count/recommendation_rate）
- `sort_order`: 排序方向（ASC/DESC）

### 4. 获取趋势分析

**GET** `/api/statistics/trend`

**查询参数：**
- `sso_school_code`: 学校ID（必填）
- `start_month`: 开始月份（必填）
- `end_month`: 结束月份（必填）
- `sso_teacher_id`: 教师ID（可选，用于教师趋势分析）
- `analysis_type`: 分析类型（school/teacher）

### 5. 获取教师评分分布

**GET** `/api/statistics/teacher/:teacherId/distribution`

### 6. 获取教师关键词云

**GET** `/api/statistics/teacher/:teacherId/keywords`

### 7. 获取学校响应趋势

**GET** `/api/statistics/school/:schoolId/trend`

### 8. 获取教师评价趋势

**GET** `/api/statistics/teacher/:teacherId/trend`

## 核心算法

### 1. 星级评分转换

```typescript
private convertRatingToScore(rating: number, starMode: StarMode): number {
  if (starMode === StarMode.FIVE_STAR) {
    return rating * 20; // 5星制：1星=20分
  } else {
    return rating * 10; // 10星制：1星=10分
  }
}
```

### 2. 评分分布计算

```sql
SELECT 
  CASE 
    WHEN score >= 90 THEN '90-100'
    WHEN score >= 80 THEN '80-89'
    WHEN score >= 70 THEN '70-79'
    WHEN score >= 60 THEN '60-69'
    ELSE '60以下'
  END as score_range,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM (
  SELECT CASE WHEN q.star_mode = 5 THEN a.rating * 20 
              ELSE a.rating * 10 END as score
  FROM answers a
  JOIN responses r ON a.response_id = r.id
  JOIN questionnaires q ON r.questionnaire_id = q.id
  WHERE a.sso_teacher_id = ?
) scores
GROUP BY score_range
```

### 3. 关键词提取算法

```typescript
private extractKeywords(text: string): string[] {
  // 预定义关键词库
  const positiveKeywords = ['认真', '负责', '专业', '耐心', '细心', ...];
  const negativeKeywords = ['不好', '差', '不满意', '改进', ...];
  const teachingKeywords = ['教学', '课堂', '作业', '学习', ...];
  
  const allKeywords = [...positiveKeywords, ...negativeKeywords, ...teachingKeywords];
  const foundKeywords: string[] = [];
  
  allKeywords.forEach(keyword => {
    if (text.includes(keyword)) {
      foundKeywords.push(keyword);
    }
  });
  
  return foundKeywords;
}
```

## 性能优化

### 1. 原生SQL查询

使用Sequelize的原生SQL查询功能，避免ORM的性能开销：

```typescript
const basicStatsQuery = `
  SELECT 
    q.sso_school_code,
    COUNT(DISTINCT r.id) as total_responses,
    ROUND(AVG(r.total_average_score), 2) as teacher_average_score
  FROM questionnaires q
  LEFT JOIN responses r ON q.id = r.questionnaire_id
  WHERE q.sso_school_code = :sso_school_code
  GROUP BY q.sso_school_code
`;

const [result] = await this.sequelize.query(basicStatsQuery, {
  type: QueryTypes.SELECT,
  replacements: { sso_school_code }
});
```

### 2. 索引优化

确保关键字段建立了适当的索引：

```sql
-- 响应表索引
CREATE INDEX idx_response_questionnaire ON responses(questionnaire_id);
CREATE INDEX idx_response_month ON responses(month);

-- 答案表索引  
CREATE INDEX idx_answer_teacher ON answers(sso_teacher_id);
CREATE INDEX idx_answer_rating ON answers(rating);

-- 问卷表索引
CREATE INDEX idx_questionnaire_school_month ON questionnaires(sso_school_code, month);
```

### 3. 查询优化策略

- **分页查询**：大数据量时使用LIMIT和OFFSET
- **条件筛选**：在WHERE子句中尽早过滤数据
- **聚合优化**：使用窗口函数计算百分比
- **缓存策略**：对频繁查询的统计数据进行缓存

## 数据验证

### 输入参数验证

```typescript
// 学校ID验证
@Rule(RuleType.string().required().max(50))
sso_school_code: string;

// 月份格式验证
@Rule(RuleType.string().optional().pattern(/^\d{4}-\d{2}$/))
month?: string;

// 排序字段验证
@Rule(RuleType.string().valid('average_score', 'evaluation_count', 'recommendation_rate'))
sort_by?: string;
```

### 业务逻辑验证

- 月份范围验证（开始月份不能大于结束月份）
- 学校ID存在性验证
- 教师ID有效性验证
- 分页参数合理性验证

## 错误处理

### 常见错误类型

1. **参数错误**
   - 必填参数缺失
   - 月份格式错误
   - 排序字段无效

2. **业务错误**
   - 学校不存在
   - 教师不存在
   - 无统计数据

3. **系统错误**
   - 数据库查询失败
   - SQL语法错误

### 错误响应格式

```json
{
  "errCode": 400,
  "msg": "未找到该学校的统计数据",
  "data": null
}
```

## 使用示例

### 前端图表集成

```javascript
// 获取学校趋势数据用于图表展示
const getSchoolTrend = async (schoolId, startMonth, endMonth) => {
  const response = await fetch(`/api/statistics/school/${schoolId}/trend?start_month=${startMonth}&end_month=${endMonth}`);
  const result = await response.json();
  
  if (result.errCode === 0) {
    // 转换为图表数据格式
    const chartData = result.data.map(item => ({
      month: item.month,
      responses: item.total_responses,
      schoolScore: item.avg_school_score,
      teacherScore: item.avg_teacher_score
    }));
    
    return chartData;
  }
};

// 获取教师排名用于排行榜展示
const getTeacherRanking = async (schoolId, month) => {
  const response = await fetch(`/api/statistics/teacher-ranking?sso_school_code=${schoolId}&month=${month}&limit=10`);
  const result = await response.json();
  
  return result.data;
};
```

## 扩展功能

### 1. 高级关键词分析

可以集成专业的中文NLP库进行更精确的关键词提取：

```typescript
// 集成jieba分词或其他NLP库
import * as jieba from 'nodejieba';

private advancedKeywordExtraction(text: string): string[] {
  // 使用jieba进行中文分词
  const words = jieba.cut(text);
  
  // 过滤停用词和无意义词汇
  const stopWords = ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'];
  
  return words.filter(word => 
    word.length > 1 && 
    !stopWords.includes(word) &&
    /[\u4e00-\u9fa5]/.test(word) // 只保留中文词汇
  );
}
```

### 2. 智能报告生成

基于统计数据自动生成分析报告：

```typescript
async generateAnalysisReport(schoolId: string, month: string): Promise<string> {
  const schoolStats = await this.getSchoolStatistics({ sso_school_code: schoolId, month });
  const teacherRanking = await this.getTeacherRanking({ sso_school_code: schoolId, month, limit: 5 });
  
  let report = `## ${schoolStats.sso_school_name} ${month} 教师评价分析报告\n\n`;
  
  report += `### 整体概况\n`;
  report += `- 总响应数：${schoolStats.total_responses}\n`;
  report += `- 完成率：${schoolStats.completion_rate}%\n`;
  report += `- 学校平均分：${schoolStats.school_average_score}分\n`;
  report += `- 教师平均分：${schoolStats.teacher_average_score}分\n\n`;
  
  report += `### 优秀教师（前5名）\n`;
  teacherRanking.list.slice(0, 5).forEach((teacher, index) => {
    report += `${index + 1}. ${teacher.sso_teacher_name}（${teacher.sso_teacher_subject}）- ${teacher.average_score}分\n`;
  });
  
  return report;
}
```

## 部署建议

1. **数据库优化**：确保统计相关表建立了适当的索引
2. **缓存策略**：对频繁查询的统计数据进行Redis缓存
3. **定时任务**：可以设置定时任务预计算月度统计数据
4. **监控告警**：监控统计查询的性能，设置慢查询告警
5. **数据备份**：定期备份统计数据，确保数据安全
