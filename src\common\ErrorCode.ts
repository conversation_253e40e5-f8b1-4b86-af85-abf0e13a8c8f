// src/common/ErrorCode.ts
export class ErrorCode {
  /**
   * 0 正常
   */
  static OK = 0;
  /**
   * 400 基本的业务异常
   */
  static BIZ_ERROR = 400;
  /**
   * 401 认证异常
   */
  static AUTH_ERROR = 401;
  /**
   * 404 Not Found异常
   */
  static NOT_FOUND = 404;
  /**
   * 422 参数校验错误
   */
  static PARAM_ERROR = 422;
  /**
   * 500 未知异常
   */
  static SYS_ERROR = 500;

  /**
   * 408 请求超时
   */
  static TIME_OUT = 408;

  /**
   * 获取错误码对应的默认消息
   */
  static getDefaultMessage(code: number): string {
    switch (code) {
      case ErrorCode.OK:
        return '操作成功';
      case ErrorCode.BIZ_ERROR:
        return '业务处理失败';
      case ErrorCode.AUTH_ERROR:
        return '认证失败';
      case ErrorCode.NOT_FOUND:
        return '资源不存在';
      case ErrorCode.PARAM_ERROR:
        return '参数校验失败';
      case ErrorCode.SYS_ERROR:
        return '系统错误';
      case ErrorCode.TIME_OUT:
        return '请求超时';
      default:
        return '未知错误';
    }
  }
}
