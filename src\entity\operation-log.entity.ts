import {
  Column,
  DataType,
  Table,
  Model,
  CreatedAt,
  UpdatedAt,
  Index,
} from 'sequelize-typescript';

export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  QUERY = 'query',
  SUBMIT = 'submit',
  PUBLISH = 'publish',
  CLOSE = 'close',
}

export enum OperationModule {
  QUESTIONNAIRE = 'questionnaire',
  RESPONSE = 'response',
  STATISTICS = 'statistics',
  SYSTEM = 'system',
}

export enum OperationStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending',
}

@Table({
  tableName: 'operation_logs',
  comment: '操作日志表',
  indexes: [
    {
      name: 'idx_operator_user_id',
      fields: ['operator_user_id'],
    },
    {
      name: 'idx_operation_time',
      fields: ['operation_time'],
    },
    {
      name: 'idx_module_type',
      fields: ['module', 'operation_type'],
    },
    {
      name: 'idx_target_id',
      fields: ['target_id', 'target_type'],
    },
  ],
})
export class OperationLog extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '日志ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '操作用户ID（SSO用户ID）',
  })
  @Index
  operator_user_id: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '操作用户姓名',
  })
  operator_user_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '操作用户角色',
  })
  operator_user_role: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '操作用户所属学校编码',
  })
  operator_school_code: string;

  @Column({
    type: DataType.ENUM(...Object.values(OperationModule)),
    allowNull: false,
    comment: '操作模块',
  })
  @Index
  module: OperationModule;

  @Column({
    type: DataType.ENUM(...Object.values(OperationType)),
    allowNull: false,
    comment: '操作类型',
  })
  @Index
  operation_type: OperationType;

  @Column({
    type: DataType.STRING(200),
    allowNull: false,
    comment: '操作描述',
  })
  operation_description: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '操作目标ID',
  })
  @Index
  target_id: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '操作目标类型',
  })
  @Index
  target_type: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '操作前数据（JSON格式）',
  })
  before_data: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '操作后数据（JSON格式）',
  })
  after_data: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '请求参数（JSON格式）',
  })
  request_params: string;

  @Column({
    type: DataType.ENUM(...Object.values(OperationStatus)),
    allowNull: false,
    defaultValue: OperationStatus.SUCCESS,
    comment: '操作状态',
  })
  operation_status: OperationStatus;

  @Column({
    type: DataType.STRING(500),
    allowNull: true,
    comment: '错误信息',
  })
  error_message: string;

  @Column({
    type: DataType.STRING(45),
    allowNull: true,
    comment: '操作IP地址',
  })
  ip_address: string;

  @Column({
    type: DataType.STRING(500),
    allowNull: true,
    comment: '用户代理信息',
  })
  user_agent: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: true,
    comment: '请求路径',
  })
  request_path: string;

  @Column({
    type: DataType.STRING(10),
    allowNull: true,
    comment: '请求方法',
  })
  request_method: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '响应时间（毫秒）',
  })
  response_time: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '操作时间',
  })
  @Index
  operation_time: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '备注信息',
  })
  remarks: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  created_at: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updated_at: Date;
}
