import { createApp, close } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';
import { ClearLogListener } from '../listener/clear-log';

describe('test/clear-log.test.ts', () => {
  let app: Application;

  beforeAll(async () => {
    // 创建应用
    app = await createApp<Framework>();
  });

  afterAll(async () => {
    // 关闭应用
    await close(app);
  });

  it('should initialize clear log listener', async () => {
    // 测试日志清理监听器的初始化
    const clearLogListener = await app.getApplicationContext().getAsync(ClearLogListener);
    
    expect(clearLogListener).toBeDefined();
    
    // 测试获取数据
    const data = clearLogListener.getData();
    expect(data).toBeDefined();
    expect(data.lastCleanupTime).toBeNull(); // 初始状态应该为null
    expect(data.nextCleanupTime).toBeDefined(); // 应该有下次清理时间
    
    // 验证下次清理时间是凌晨2点
    if (data.nextCleanupTime) {
      const nextCleanup = new Date(data.nextCleanupTime);
      expect(nextCleanup.getHours()).toBe(2);
      expect(nextCleanup.getMinutes()).toBe(0);
      expect(nextCleanup.getSeconds()).toBe(0);
    }
  });

  it('should calculate next cleanup time correctly', async () => {
    const clearLogListener = await app.getApplicationContext().getAsync(ClearLogListener);
    const data = clearLogListener.getData();
    
    if (data.nextCleanupTime) {
      const now = new Date();
      const nextCleanup = new Date(data.nextCleanupTime);
      
      // 下次清理时间应该在未来
      expect(nextCleanup.getTime()).toBeGreaterThan(now.getTime());
      
      // 下次清理时间应该是凌晨2点
      expect(nextCleanup.getHours()).toBe(2);
      expect(nextCleanup.getMinutes()).toBe(0);
      expect(nextCleanup.getSeconds()).toBe(0);
    }
  });
});
