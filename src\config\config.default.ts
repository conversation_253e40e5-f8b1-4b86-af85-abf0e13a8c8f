import { MidwayConfig } from '@midwayjs/core';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1748424677125_870',
  koa: {
    port: 3141, // 教师测评后台端口为3140，对应的问卷后台使用3141
  },
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'Clouddeep@8890',
        database: 'teacher-evaluation-question',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        logging: false,
        repositoryMode: true,
      },
    },
  },
  axios: {
    clients: {
      apiManager: {
        baseURL: 'http://127.0.0.1:1002',
        timeout: 10000,
      },
    },
  },

  /** jwt 认证组件配置 start */
  // JWT配置（必需）
  jwt: {
    secret: 'question-secret',
    expiresIn: '2h', // 访问令牌过期时间
  },
  // 令牌配置（必需）
  token: {
    secret: 'your-token-secret',
    expiresIn: 2 * 3600, // 访问令牌过期时间（秒）
    refreshExpiresIn: 15 * 24 * 3600, // 刷新令牌过期时间（秒）
  },
  // JWT认证组件配置
  jwtAuth: {
    enable: true,
    // 只配置需要覆盖的项
    apiManagerBaseURL: 'http://127.0.0.1:1002', // 根据实际环境修改
    // 白名单路径配置
    whitelist: [
      '/auth/login',
      '/auth/register',
      '/auth/refresh-token',
      '/api/parent/verify-phone', // 家长手机号验证接口
      '/api/parent/student-questionnaires', // 查询学生问卷接口
      '/public',
      '/weapp',
      '/openapi',
      '/dev-tools',
      '/wepay',
    ],
  },
  /** jwt 认证组件配置 end */
} as MidwayConfig;
