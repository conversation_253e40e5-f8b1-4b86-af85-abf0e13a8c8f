import { Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AuthError } from '../error/custom.error';
import { OperationLogService } from '../service/operation-log.service';
import { OperationModule, OperationType } from '../entity/operation-log.entity';

/**
 * 基础Controller类，提供通用方法
 */
export abstract class BaseController {
  @Inject()
  ctx: Context;

  @Inject()
  operationLogService: OperationLogService;

  /**
   * 获取当前登录用户信息
   */
  protected getCurrentUser(): { userId: string; userName: string } {
    const userInfo = this.ctx.state.user || {};
    let userId = userInfo.userId || userInfo.id;
    let userName = userInfo.userName || userInfo.name;

    // 如果JWT中没有用户信息，尝试从header中获取（用于测试）
    if (!userId) {
      userId = this.ctx.request.headers['x-user-id'] as string;
      userName = this.ctx.request.headers['x-user-name'] as string;
    }

    if (!userId) {
      throw new AuthError('用户未登录或token无效');
    }

    return { userId, userName };
  }

  /**
   * 获取当前用户ID
   */
  protected getCurrentUserId(): string {
    return this.getCurrentUser().userId;
  }

  /**
   * 获取当前用户名
   */
  protected getCurrentUserName(): string {
    return this.getCurrentUser().userName;
  }

  /**
   * 记录操作日志
   * @param operation 操作描述
   * @param details 操作详情
   * @param module 操作模块，默认为QUESTIONNAIRE
   * @param operationType 操作类型，默认为OTHER
   */
  protected async logOperation(
    operation: string,
    details?: any,
    module: OperationModule = OperationModule.QUESTIONNAIRE,
    operationType: OperationType = OperationType.CREATE
  ) {
    try {
      // 记录到应用日志
      this.ctx.logger.info(`用户操作: ${operation}`, {
        userId: this.getCurrentUserId(),
        operation,
        details,
        timestamp: new Date().toISOString(),
      });

      // 记录到数据库
      const { userId, userName } = this.getCurrentUser();
      await this.operationLogService.logOperation(
        this.ctx,
        userId,
        module,
        operationType,
        operation,
        {
          targetId:
            details?.questionnaireId?.toString() || details?.id?.toString(),
          targetType: details?.questionnaireId ? 'questionnaire' : 'unknown',
          afterData: details,
          operatorUserName: userName,
          // 添加请求参数，使日志更完整
          requestParams: this.getRequestParams(),
        }
      );
    } catch (error) {
      // 日志记录失败不应该影响主业务流程
      this.ctx.logger.error('记录操作日志失败:', error);
    }
  }

  /**
   * 获取请求参数
   * @returns 请求参数
   */
  private getRequestParams(): any {
    const params: any = {};

    // 获取query参数
    if (Object.keys(this.ctx.request.query).length > 0) {
      params.query = this.ctx.request.query;
    }

    // 获取body参数（排除敏感信息）
    if (
      this.ctx.request.body &&
      typeof this.ctx.request.body === 'object' &&
      Object.keys(this.ctx.request.body).length > 0
    ) {
      const body = { ...(this.ctx.request.body as any) };

      // 移除敏感信息
      delete body.password;
      delete body.token;
      delete body.secret;

      params.body = body;
    }

    // 获取路径参数
    if (this.ctx.params && Object.keys(this.ctx.params).length > 0) {
      params.params = this.ctx.params;
    }

    return Object.keys(params).length > 0 ? params : undefined;
  }
}
