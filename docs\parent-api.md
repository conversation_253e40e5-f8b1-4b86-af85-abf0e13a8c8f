# 家长相关API文档

## 1. 验证家长手机号

**POST** `/api/parent/verify-phone`

### 功能说明
通过查询该手机号下的学生信息来验证家长手机号是否有效。此接口不需要登录认证。

### 请求参数

**Content-Type:** `application/json`

```json
{
  "phone": "13800138000"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 家长手机号，必须符合中国手机号格式 |

### 手机号格式要求
- 必须是11位数字
- 以1开头，第二位为3-9之间的数字
- 正则表达式：`/^1[3-9]\d{9}$/`

### 响应格式

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "手机号验证成功",
  "data": {
    "is_valid": true,
    "message": "验证成功",
    "parent": {
      "id": "parent001",
      "mobile": "13800138000",
      "name": "张家长",
      "children": [
        {
          "id": "child001",
          "student": {
            "id": "student001",
            "name": "张小明",
            "class": "三年级1班"
          }
        }
      ]
    }
  },
  "timestamp": 1640995200000
}
```

**验证失败响应：**
```json
{
  "errCode": 0,
  "msg": "手机号验证失败",
  "data": {
    "is_valid": false,
    "message": "手机号验证失败，请确认手机号是否正确"
  },
  "timestamp": 1640995200000
}
```

### 使用示例

**curl 示例：**
```bash
curl -X POST http://localhost:7001/api/parent/verify-phone \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000"}'
```

## 2. 查询学生问卷

**GET** `/api/parent/student-questionnaires`

### 功能说明
根据学校编码、学生ID、月份查询该学生是否有问卷可填写。此接口不需要登录认证。

### 请求参数

**查询参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sso_school_code | string | 是 | 学校编码 |
| sso_student_id | string | 是 | 学生ID |
| month | string | 是 | 月份，格式：YYYY-MM |
| parent_phone | string | 否 | 家长手机号（用于检查是否已提交） |

### 参数格式要求
- `month`: 必须符合 YYYY-MM 格式，如 "2024-01"
- `parent_phone`: 如果提供，必须符合中国手机号格式

### 响应格式

**有问卷且未提交：**
```json
{
  "errCode": 0,
  "msg": "查询完成",
  "data": {
    "has_questionnaire": true,
    "message": "找到可填写的问卷",
    "questionnaire": {
      "id": 1,
      "title": "2024年1月教师评价问卷",
      "description": "请对本月教师表现进行评价",
      "month": "2024-01",
      "star_mode": 5,
      "sso_school_code": "school_001",
      "sso_school_name": "示例学校",
      "start_time": "2024-01-01T00:00:00Z",
      "end_time": "2024-01-31T23:59:59Z",
      "instructions": "请客观公正地评价",
      "max_teachers_limit": 10,
      "is_submitted": false
    }
  },
  "timestamp": 1640995200000
}
```

**无问卷：**
```json
{
  "errCode": 0,
  "msg": "查询完成",
  "data": {
    "has_questionnaire": false,
    "message": "该学校在指定月份暂无可填写的问卷"
  },
  "timestamp": 1640995200000
}
```

### 使用示例

**curl 示例：**
```bash
curl "http://localhost:7001/api/parent/student-questionnaires?sso_school_code=school_001&sso_student_id=student_001&month=2024-01&parent_phone=13800138000"
```

**JavaScript 示例：**
```javascript
const response = await fetch('/api/parent/student-questionnaires?' + new URLSearchParams({
  sso_school_code: 'school_001',
  sso_student_id: 'student_001',
  month: '2024-01',
  parent_phone: '13800138000'
}));

const result = await response.json();
if (result.errCode === 0) {
  if (result.data.has_questionnaire) {
    if (result.data.questionnaire.is_submitted) {
      console.log('问卷已提交');
    } else {
      console.log('找到可填写的问卷：', result.data.questionnaire);
    }
  } else {
    console.log('暂无可填写的问卷');
  }
}
```

### 业务逻辑说明

1. **查询逻辑**：
   - 根据学校编码和月份查询已发布状态的问卷
   - 如果提供了家长手机号，会检查该学生是否已提交问卷

2. **状态判断**：
   - `has_questionnaire: false`: 该学校该月份没有已发布的问卷
   - `has_questionnaire: true, is_submitted: false`: 有问卷且未提交
   - `has_questionnaire: true, is_submitted: true`: 有问卷但已提交

3. **注意事项**：
   - 此接口已加入JWT认证白名单，无需登录即可访问
   - 只查询状态为"已发布"的问卷
   - 如果不提供家长手机号，`is_submitted` 始终为 false

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |
