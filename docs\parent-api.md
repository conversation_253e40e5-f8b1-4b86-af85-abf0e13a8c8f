# 家长相关API文档

## 1. 验证家长手机号

**POST** `/api/parent/verify-phone`

### 功能说明
通过查询该手机号下的学生信息来验证家长手机号是否有效。此接口不需要登录认证。

### 请求参数

**Content-Type:** `application/json`

```json
{
  "phone": "13800138000"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 家长手机号，必须符合中国手机号格式 |

### 手机号格式要求
- 必须是11位数字
- 以1开头，第二位为3-9之间的数字
- 正则表达式：`/^1[3-9]\d{9}$/`

### 响应格式

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "手机号验证成功",
  "data": {
    "is_valid": true,
    "message": "验证成功，该手机号下共有 2 名学生",
    "children": [
      {
        "id": "student001",
        "name": "张小明",
        "class": "三年级1班",
        "grade": "三年级",
        "school_code": "school001"
      },
      {
        "id": "student002", 
        "name": "张小红",
        "class": "一年级2班",
        "grade": "一年级",
        "school_code": "school001"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

**验证失败响应：**
```json
{
  "errCode": 0,
  "msg": "手机号验证失败",
  "data": {
    "is_valid": false,
    "message": "该手机号未绑定任何学生信息，请确认手机号是否正确"
  },
  "timestamp": 1640995200000
}
```

**参数验证失败响应：**
```json
{
  "errCode": 422,
  "msg": "家长手机号格式不正确",
  "data": null,
  "timestamp": 1640995200000
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| is_valid | boolean | 手机号是否有效 |
| message | string | 验证结果描述信息 |
| children | array | 学生信息列表（仅在验证成功时返回） |
| children[].id | string | 学生ID |
| children[].name | string | 学生姓名 |
| children[].class | string | 班级信息 |
| children[].grade | string | 年级信息 |
| children[].school_code | string | 学校编码 |

### 使用示例

**curl 示例：**
```bash
curl -X POST http://localhost:7001/api/parent/verify-phone \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000"}'
```

**JavaScript 示例：**
```javascript
const response = await fetch('/api/parent/verify-phone', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    phone: '13800138000'
  })
});

const result = await response.json();
if (result.errCode === 0 && result.data.is_valid) {
  console.log('验证成功，学生列表：', result.data.children);
} else {
  console.log('验证失败：', result.data.message);
}
```

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

### 注意事项

1. 此接口已加入JWT认证白名单，无需登录即可访问
2. 接口会记录访问日志，包括请求的手机号和验证结果
3. 如果SSO服务异常，会返回验证失败的结果
4. 手机号格式验证在服务端进行，前端也应该进行相应的格式检查
