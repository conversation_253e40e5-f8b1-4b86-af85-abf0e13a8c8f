/*
 * @Description: 通过兴教云接口中心发送请求
 * @Date: 2025-03-29 10:17:13
 * @LastEditors: ZhuPengliang <EMAIL>
 * @LastEditTime: 2025-05-31 13:50:23
 */

import { HttpService, HttpServiceFactory } from '@midwayjs/axios';
import { Inject, InjectClient, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';

@Provide()
export class APIManager {
  @InjectClient(HttpServiceFactory, 'apiManager')
  apiManager: HttpService;

  @Inject()
  ctx: Context;

  async send(data: {
    apiCode: string;
    query?: Record<string, string | number>;
    params?: string;
    body?: Record<string, any>;
  }) {
    console.dir(data);
    if (!data.apiCode) {
      throw new Error('apiCode is required');
    }
    const { status, data: sourceData } = await this.apiManager.request({
      url: '/apis/request',
      method: 'POST',
      data,
    });
    if (status !== 200) {
      this.ctx.logger.error({
        message: '请求失败',
        source: '接口',
        data: {
          params: data,
          status,
        },
      });
      throw new Error('请求失败');
    }
    const { errCode, msg, data: returnData } = sourceData;
    if (errCode !== 0) {
      console.log('api调用返回错误');
      console.log(sourceData);
      this.ctx.logger.error({
        message: msg,
        source: '接口',
        data: {
          params: data,
          status,
          data: sourceData,
        },
      });
      throw new Error(msg);
    }
    return returnData;
  }
}
