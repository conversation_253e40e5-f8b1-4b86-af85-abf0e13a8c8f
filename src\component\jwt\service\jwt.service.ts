/*
 * @Description: JWT认证服务
 * @Date: 2025-05-15
 */
import { Config, Inject, Provide } from '@midwayjs/core';
import { JwtService } from '@midwayjs/jwt';
import { Context } from '@midwayjs/koa';
import {
  JwtAuthConfig,
  JwtLoginParams,
  JwtLoginResult,
  JwtRefreshTokenParams,
  JwtRefreshTokenResult,
  JwtTokenPayload,
  JwtUserInfo,
} from '../interface';
import { JwtAuthApiService } from './auth.service';
import { JwtCustomError } from '../common/error';

@Provide()
export class JwtAuthService {
  @Inject()
  ctx: Context;

  @Inject()
  jwtService: JwtService;

  @Config('jwtAuth')
  jwtAuthConfig: JwtAuthConfig;

  @Config('jwt')
  jwtConfig: any;

  @Config('token')
  tokenConfig: any;

  @Inject()
  jwtAuthApiService: JwtAuthApiService;

  /**
   * 用户登录
   * @param params 登录参数
   */
  async login(params: JwtLoginParams): Promise<JwtLoginResult> {
    const { enterpriseCode, userCode } = params;

    // 1. 通过用户信息服务获取用户信息
    const userInfo = await this.jwtAuthApiService.postByUserCodes(
      enterpriseCode,
      userCode
    );

    if (!userInfo) {
      throw new JwtCustomError('用户不存在或企业编码错误', 401);
    }

    // 2. 生成 token
    const { accessToken, refreshToken } = await this.generateTokens({
      userId: userInfo.id || userInfo.code,
      username: userInfo.username || userInfo.name,
      userCode: userInfo.code,
      enterpriseCode,
      roles: userInfo.memberTypes?.map((type: any) => type.type_code) || [],
    });

    return {
      accessToken,
      refreshToken,
      userInfo: {
        id: userInfo.id || userInfo.code,
        userCode: userInfo.code,
        username: userInfo.username || userInfo.name,
        realName: userInfo.name,
        enterpriseCode,
        enterpriseName: userInfo.enterpriseName,
        roles: userInfo.memberTypes?.map((type: any) => type.type_code) || [],
        mobile: userInfo.mobile,
        email: userInfo.email,
        enterprise: {
          id: userInfo.enterprise.id,
          code: userInfo.enterprise.code,
          name: userInfo.enterprise.name,
          type_code: userInfo.enterprise.type_code,
          type_name: userInfo.enterprise.type_name,
          region: userInfo.enterprise.region,
          region_code: userInfo.enterprise.region_code,
        },
      },
    };
  }

  /**
   * 刷新令牌
   * @param params 刷新令牌参数
   */
  async refreshToken(
    params: JwtRefreshTokenParams
  ): Promise<JwtRefreshTokenResult> {
    const { refreshToken } = params;

    try {
      // 1. 验证刷新令牌
      const decoded = await this.jwtService.verify(refreshToken);
      const payload = decoded as unknown as JwtTokenPayload;

      if (payload.type !== 'refresh') {
        throw new JwtCustomError('无效的刷新令牌', 401);
      }

      // 2. 获取用户信息
      const userInfo = await this.jwtAuthApiService.postByUserCodes(
        payload.enterpriseCode,
        payload.userCode
      );

      if (!userInfo) {
        throw new JwtCustomError('用户不存在或企业编码错误', 401);
      }

      // 3. 生成新的令牌
      return await this.generateTokens({
        userId: userInfo.id || userInfo.code,
        username: userInfo.username || userInfo.name,
        userCode: userInfo.code,
        enterpriseCode: payload.enterpriseCode,
        roles: userInfo.memberTypes?.map((type: any) => type.type_code) || [],
      });
    } catch (error) {
      if (error instanceof JwtCustomError) {
        throw error;
      }
      throw new JwtCustomError('刷新令牌无效或已过期', 401);
    }
  }

  /**
   * 生成令牌
   * @param user 用户信息
   */
  async generateTokens(user: {
    userId: string;
    username: string;
    userCode: string;
    enterpriseCode: string;
    roles: string[];
  }): Promise<JwtRefreshTokenResult> {
    // 1. 生成访问令牌
    const accessTokenPayload: JwtTokenPayload = {
      userId: user.userId,
      username: user.username,
      roles: user.roles,
      type: 'access',
      userCode: user.userCode,
      enterpriseCode: user.enterpriseCode,
    };

    const accessToken = this.jwtService.signSync(accessTokenPayload, {
      expiresIn: this.jwtConfig.expiresIn || '2h',
    });

    // 2. 生成刷新令牌
    const refreshTokenPayload: JwtTokenPayload = {
      userId: user.userId,
      type: 'refresh',
      userCode: user.userCode,
      enterpriseCode: user.enterpriseCode,
    };

    const refreshToken = this.jwtService.signSync(refreshTokenPayload, {
      expiresIn: `${this.tokenConfig.refreshExpiresIn || 15 * 24 * 3600}s`,
    });

    return { accessToken, refreshToken };
  }

  /**
   * 验证令牌
   * @param token 令牌
   */
  async verifyToken(token: string): Promise<JwtTokenPayload> {
    try {
      const decoded = await this.jwtService.verify(token);
      return decoded as unknown as JwtTokenPayload;
    } catch (error) {
      throw new JwtCustomError('令牌无效或已过期', 401);
    }
  }

  /**
   * 获取当前用户
   */
  async getCurrentUser(): Promise<JwtUserInfo> {
    const user = this.ctx.state.user;
    if (!user) {
      throw new JwtCustomError('未登录', 401);
    }

    // 从用户信息服务获取最新的用户信息
    const userInfo = await this.jwtAuthApiService.postByUserCodes(
      user.enterpriseCode,
      user.userCode
    );

    if (!userInfo) {
      throw new JwtCustomError('用户不存在', 401);
    }

    return {
      id: userInfo.id || userInfo.code,
      userCode: userInfo.code,
      username: userInfo.username || userInfo.name,
      realName: userInfo.name,
      enterpriseCode: user.enterpriseCode,
      enterpriseName: userInfo.enterpriseName,
      roles: userInfo.memberTypes?.map((type: any) => type.type_code) || [],
      mobile: userInfo.mobile,
      email: userInfo.email,
    };
  }

  /**
   * 退出登录
   */
  async logout(): Promise<boolean> {
    // 由于不存储用户信息，这里不需要做任何操作
    // 客户端需要自行清除token
    return true;
  }
}
