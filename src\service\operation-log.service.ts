import { Provide, Scope, ScopeEnum } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize, QueryTypes, Op } from 'sequelize';
import { Context } from '@midwayjs/koa';
import {
  OperationLog,
  OperationType,
  OperationModule,
  OperationStatus,
} from '../entity/operation-log.entity';
import {
  CreateOperationLogDTO,
  QueryOperationLogDTO,
  OperationLogStatisticsDTO,
} from '../dto/operation-log.dto';
import { CustomError } from '../error/custom.error';
import {
  IOperationLogListResponse,
  IOperationLogStatistics,
} from '../interface';

@Provide()
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class OperationLogService {
  @InjectRepository(OperationLog)
  operationLogRepository: Repository<OperationLog>;

  @InjectDataSource()
  sequelize: Sequelize;

  /**
   * 创建操作日志
   * @param createDto 创建参数
   * @returns 操作日志记录
   */
  async createOperationLog(
    createDto: CreateOperationLogDTO
  ): Promise<OperationLog> {
    try {
      const operationLog = await this.operationLogRepository.create({
        ...createDto,
        operation_time: createDto.operation_time || new Date(),
      });

      return operationLog;
    } catch (error) {
      throw new CustomError(`创建操作日志失败: ${error.message}`);
    }
  }

  /**
   * 记录操作日志（便捷方法）
   * @param ctx Koa上下文
   * @param operatorUserId 操作用户ID
   * @param module 操作模块
   * @param operationType 操作类型
   * @param description 操作描述
   * @param options 其他选项
   */
  async logOperation(
    ctx: Context,
    operatorUserId: string,
    module: OperationModule,
    operationType: OperationType,
    description: string,
    options: {
      targetId?: string;
      targetType?: string;
      beforeData?: any;
      afterData?: any;
      requestParams?: any;
      operationStatus?: OperationStatus;
      errorMessage?: string;
      responseTime?: number;
      operatorUserName?: string;
      operatorUserRole?: string;
      operatorSchoolCode?: string;
      remarks?: string;
    } = {}
  ): Promise<void> {
    try {
      // 获取请求信息
      const clientIP =
        ctx.request.ip ||
        (Array.isArray(ctx.request.headers['x-forwarded-for'])
          ? ctx.request.headers['x-forwarded-for'][0]
          : ctx.request.headers['x-forwarded-for']) ||
        (Array.isArray(ctx.request.headers['x-real-ip'])
          ? ctx.request.headers['x-real-ip'][0]
          : ctx.request.headers['x-real-ip']) ||
        'unknown';

      const userAgent = ctx.request.headers['user-agent'] || '';
      const requestPath = ctx.request.path || '';
      const requestMethod = ctx.request.method || '';

      const logData: CreateOperationLogDTO = {
        operator_user_id: operatorUserId,
        operator_user_name: options.operatorUserName,
        operator_user_role: options.operatorUserRole,
        operator_school_code: options.operatorSchoolCode,
        module,
        operation_type: operationType,
        operation_description: description,
        target_id: options.targetId,
        target_type: options.targetType,
        before_data: options.beforeData
          ? JSON.stringify(options.beforeData)
          : undefined,
        after_data: options.afterData
          ? JSON.stringify(options.afterData)
          : undefined,
        request_params: options.requestParams
          ? JSON.stringify(options.requestParams)
          : undefined,
        operation_status: options.operationStatus || OperationStatus.SUCCESS,
        error_message: options.errorMessage,
        ip_address: clientIP,
        user_agent: userAgent,
        request_path: requestPath,
        request_method: requestMethod,
        response_time: options.responseTime,
        operation_time: new Date(),
        remarks: options.remarks,
      };

      // 异步创建日志，不阻塞主流程
      setImmediate(() => {
        this.createOperationLog(logData).catch(error => {
          console.error('创建操作日志失败:', error);
        });
      });
    } catch (error) {
      // 日志记录失败不应该影响主业务流程
      console.error('记录操作日志失败:', error);
    }
  }

  /**
   * 获取操作日志列表
   * @param queryDto 查询条件
   * @returns 操作日志列表
   */
  async getOperationLogList(
    queryDto: QueryOperationLogDTO
  ): Promise<IOperationLogListResponse> {
    const {
      operator_user_id,
      operator_school_code,
      module,
      operation_type,
      target_id,
      target_type,
      operation_status,
      ip_address,
      start_time,
      end_time,
      page = 1,
      limit = 20,
      sort_by = 'operation_time',
      sort_order = 'DESC',
    } = queryDto;

    // 构建查询条件
    const whereConditions: any = {};

    if (operator_user_id) {
      whereConditions.operator_user_id = operator_user_id;
    }

    if (operator_school_code) {
      whereConditions.operator_school_code = operator_school_code;
    }

    if (module) {
      whereConditions.module = module;
    }

    if (operation_type) {
      whereConditions.operation_type = operation_type;
    }

    if (target_id) {
      whereConditions.target_id = target_id;
    }

    if (target_type) {
      whereConditions.target_type = target_type;
    }

    if (operation_status) {
      whereConditions.operation_status = operation_status;
    }

    if (ip_address) {
      whereConditions.ip_address = ip_address;
    }

    if (start_time && end_time) {
      whereConditions.operation_time = {
        [Op.between]: [start_time, end_time],
      };
    } else if (start_time) {
      whereConditions.operation_time = {
        [Op.gte]: start_time,
      };
    } else if (end_time) {
      whereConditions.operation_time = {
        [Op.lte]: end_time,
      };
    }

    // 执行查询
    const { rows: list, count: total } =
      await this.operationLogRepository.findAndCountAll({
        where: whereConditions,
        order: [[sort_by, sort_order]],
        limit,
        offset: (page - 1) * limit,
      });

    return {
      list,
      total,
      page,
      limit,
    };
  }

  /**
   * 根据ID获取操作日志详情
   * @param id 日志ID
   * @returns 操作日志详情
   */
  async getOperationLogById(id: number): Promise<OperationLog> {
    const operationLog = await this.operationLogRepository.findByPk(id);

    if (!operationLog) {
      throw new CustomError('操作日志不存在');
    }

    return operationLog;
  }

  /**
   * 获取操作日志统计
   * @param queryDto 查询条件
   * @returns 统计数据
   */
  async getOperationLogStatistics(
    queryDto: OperationLogStatisticsDTO
  ): Promise<IOperationLogStatistics> {
    const {
      operator_school_code,
      module,
      start_time,
      end_time,
      granularity = 'day',
    } = queryDto;

    // 构建基础查询条件
    let whereClause = '1=1';
    const replacements: any = {};

    if (operator_school_code) {
      whereClause += ' AND operator_school_code = :operator_school_code';
      replacements.operator_school_code = operator_school_code;
    }

    if (module) {
      whereClause += ' AND module = :module';
      replacements.module = module;
    }

    if (start_time && end_time) {
      whereClause += ' AND operation_time BETWEEN :start_time AND :end_time';
      replacements.start_time = start_time;
      replacements.end_time = end_time;
    }

    // 基础统计查询
    const basicStatsQuery = `
      SELECT
        COUNT(*) as total_operations,
        COUNT(CASE WHEN operation_status = 'success' THEN 1 END) as success_operations,
        COUNT(CASE WHEN operation_status = 'failed' THEN 1 END) as failed_operations,
        ROUND(AVG(response_time), 2) as avg_response_time
      FROM operation_logs
      WHERE ${whereClause}
    `;

    const [basicStats] = (await this.sequelize.query(basicStatsQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    const totalOperations = parseInt(basicStats.total_operations) || 0;
    const successOperations = parseInt(basicStats.success_operations) || 0;
    const failedOperations = parseInt(basicStats.failed_operations) || 0;
    const successRate =
      totalOperations > 0
        ? Math.round((successOperations / totalOperations) * 100 * 100) / 100
        : 0;

    // 操作趋势查询
    const trendQuery = this.buildTrendQuery(granularity, whereClause);
    const operationTrend = (await this.sequelize.query(trendQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    // 先获取总数用于计算百分比
    const totalCountQuery = `
      SELECT COUNT(*) as total_count
      FROM operation_logs
      WHERE ${whereClause}
    `;

    const [totalCountResult] = (await this.sequelize.query(totalCountQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    const totalCount = parseInt(totalCountResult?.total_count) || 0;

    // 模块分布查询
    const moduleDistributionQuery = `
      SELECT
        module,
        COUNT(*) as count
      FROM operation_logs
      WHERE ${whereClause}
      GROUP BY module
      ORDER BY count DESC
    `;

    const moduleDistribution = (await this.sequelize.query(
      moduleDistributionQuery,
      {
        type: QueryTypes.SELECT,
        replacements,
      }
    )) as any[];

    // 计算百分比
    const moduleDistributionWithPercentage = moduleDistribution.map(item => ({
      module: item.module,
      count: parseInt(item.count) || 0,
      percentage:
        totalCount > 0
          ? Math.round((parseInt(item.count) / totalCount) * 100 * 100) / 100
          : 0,
    }));

    // 用户活跃度查询
    const userActivityQuery = `
      SELECT
        operator_user_id,
        operator_user_name,
        COUNT(*) as operation_count,
        COUNT(CASE WHEN operation_status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN operation_status = 'failed' THEN 1 END) as failed_count,
        MAX(operation_time) as last_operation_time
      FROM operation_logs
      WHERE ${whereClause}
      GROUP BY operator_user_id, operator_user_name
      ORDER BY operation_count DESC
      LIMIT 20
    `;

    const userActivity = (await this.sequelize.query(userActivityQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    return {
      total_operations: totalOperations,
      success_operations: successOperations,
      failed_operations: failedOperations,
      success_rate: successRate,
      avg_response_time: parseFloat(basicStats.avg_response_time) || 0,
      operation_trend: operationTrend,
      module_distribution: moduleDistributionWithPercentage,
      user_activity: userActivity,
    };
  }

  /**
   * 构建趋势查询SQL
   * @param granularity 时间粒度
   * @param whereClause 查询条件
   * @returns SQL查询语句
   */
  private buildTrendQuery(granularity: string, whereClause: string): string {
    let dateFormat: string;

    switch (granularity) {
      case 'day':
        dateFormat = '%Y-%m-%d';
        break;
      case 'week':
        dateFormat = '%Y-%u';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    return `
      SELECT
        DATE_FORMAT(operation_time, '${dateFormat}') as time_period,
        COUNT(*) as total_count,
        COUNT(CASE WHEN operation_status = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN operation_status = 'failed' THEN 1 END) as failed_count,
        ROUND(AVG(response_time), 2) as avg_response_time
      FROM operation_logs
      WHERE ${whereClause}
      GROUP BY time_period
      ORDER BY time_period
    `;
  }

  /**
   * 删除过期的操作日志
   * @param daysToKeep 保留天数
   * @returns 删除的记录数
   */
  async cleanupExpiredLogs(daysToKeep = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const deletedCount = await this.operationLogRepository.destroy({
      where: {
        operation_time: {
          [Op.lt]: cutoffDate,
        },
      },
    });

    return deletedCount;
  }
}
