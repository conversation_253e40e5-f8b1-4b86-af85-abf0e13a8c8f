# 教师评价问卷统计分析模块

## 🎯 项目概述

本项目实现了完整的教师评价问卷统计分析系统，提供学校维度和教师维度的多层次数据分析功能。系统采用 Midway.js + TypeScript + Sequelize + MySQL 技术栈，具备高性能、高可靠性的特点。

## ✨ 核心功能

### 🏫 学校维度统计
- **整体概况分析**：学校平均分、完成率、响应数量统计
- **趋势分析**：按月份展示评分趋势变化
- **教师排名**：基于平均分的教师排行榜
- **对比分析**：多月份数据对比

### 👨‍🏫 教师维度统计
- **个人评价分析**：平均分、推荐率、评价数量
- **评分分布**：90-100分、80-89分等各分数段占比
- **关键词云**：从评价描述中提取关键词
- **细分评分**：教学质量、教学态度、课堂管理等维度
- **趋势跟踪**：教师个人评分变化趋势

### 📊 高级分析功能
- **多维度筛选**：支持按学校、月份、科目、部门筛选
- **灵活排序**：支持按平均分、评价数量、推荐率排序
- **性能优化**：使用原生SQL查询，支持大数据量分析
- **实时统计**：基于最新数据的实时统计分析

## 🏗️ 系统架构

```
├── src/
│   ├── controller/
│   │   └── statistics.controller.ts    # 统计控制器
│   ├── service/
│   │   └── statistics.service.ts       # 统计服务
│   ├── dto/
│   │   └── statistics.dto.ts           # 统计DTO
│   ├── entity/
│   │   ├── response.entity.ts          # 响应实体
│   │   ├── answer.entity.ts            # 答案实体
│   │   └── questionnaire.entity.ts     # 问卷实体
│   └── interface.ts                    # 接口定义
├── test/
│   └── statistics.test.ts              # 统计测试
└── docs/
    └── statistics-module.md            # 详细文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置数据库

```typescript
// src/config/config.local.ts
export default {
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: 'your-host',
        port: 3306,
        username: 'your-username',
        password: 'your-password',
        database: 'teacher-evaluation-question'
      }
    }
  }
}
```

### 3. 运行项目

```bash
# 开发模式
npm run dev

# 编译
npm run build

# 测试
npm test
```

## 📋 API 接口

### 学校统计接口

```http
GET /api/statistics/school?sso_school_code=school_001&month=2024-01
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "获取学校统计数据成功",
  "data": {
    "sso_school_code": "school_001",
    "sso_school_name": "示例小学",
    "total_responses": 150,
    "completed_responses": 142,
    "completion_rate": 94.67,
    "school_average_score": 87.5,
    "teacher_average_score": 89.2,
    "total_teachers_evaluated": 25
  }
}
```

### 教师统计接口

```http
GET /api/statistics/teacher?sso_teacher_id=teacher_001&include_distribution=true
```

### 教师排名接口

```http
GET /api/statistics/teacher-ranking?sso_school_code=school_001&sort_by=average_score
```

### 趋势分析接口

```http
GET /api/statistics/trend?sso_school_code=school_001&start_month=2024-01&end_month=2024-03
```

## 🔧 核心算法

### 星级评分转换

```typescript
// 5星制：1星=20分，2星=40分，...，5星=100分
// 10星制：1星=10分，2星=20分，...，10星=100分
const score = starMode === 5 ? rating * 20 : rating * 10;
```

### 评分分布计算

```sql
SELECT 
  CASE 
    WHEN score >= 90 THEN '90-100'
    WHEN score >= 80 THEN '80-89'
    WHEN score >= 70 THEN '70-79'
    WHEN score >= 60 THEN '60-69'
    ELSE '60以下'
  END as score_range,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / total_count, 2) as percentage
FROM score_data
GROUP BY score_range
```

### 关键词提取

```typescript
// 基于预定义词库的关键词提取
const keywords = ['认真', '负责', '专业', '耐心', '教学', '课堂'];
const foundKeywords = keywords.filter(keyword => text.includes(keyword));
```

## 📊 数据模型

### 响应表 (responses)
- 存储家长提交的问卷响应
- 包含学校评分、学生信息、完成状态等

### 答案表 (answers)
- 存储对具体教师的评价
- 包含评分、描述、标签、推荐状态等

### 问卷表 (questionnaires)
- 存储问卷基本信息
- 包含星级模式、学校信息、时间范围等

## 🎨 前端集成示例

### 获取学校趋势图表数据

```javascript
const getSchoolTrendChart = async (schoolId, startMonth, endMonth) => {
  const response = await fetch(`/api/statistics/school/${schoolId}/trend?start_month=${startMonth}&end_month=${endMonth}`);
  const result = await response.json();
  
  return result.data.map(item => ({
    month: item.month,
    schoolScore: item.avg_school_score,
    teacherScore: item.avg_teacher_score,
    responses: item.total_responses
  }));
};
```

### 教师排名表格

```javascript
const getTeacherRankingTable = async (schoolId, month) => {
  const response = await fetch(`/api/statistics/teacher-ranking?sso_school_code=${schoolId}&month=${month}&limit=20`);
  const result = await response.json();
  
  return result.data.list.map(teacher => ({
    rank: teacher.rank,
    name: teacher.sso_teacher_name,
    subject: teacher.sso_teacher_subject,
    score: teacher.average_score,
    evaluations: teacher.evaluation_count,
    recommendation: teacher.recommendation_rate
  }));
};
```

## 🔍 测试覆盖

- ✅ **接口测试**：所有REST接口的功能测试
- ✅ **业务逻辑测试**：核心算法和计算逻辑验证
- ✅ **数据验证测试**：DTO结构和格式验证
- ✅ **错误处理测试**：异常情况的处理验证

```bash
# 运行统计模块测试
npm test -- test/statistics.test.ts
```

## ⚡ 性能优化

### 1. 数据库优化
- **索引策略**：在关键查询字段建立复合索引
- **查询优化**：使用原生SQL避免ORM性能损耗
- **分页查询**：大数据量时使用LIMIT和OFFSET

### 2. 缓存策略
- **Redis缓存**：缓存频繁查询的统计结果
- **定时更新**：设置定时任务预计算统计数据
- **分层缓存**：应用层和数据库层多级缓存

### 3. 查询优化
```sql
-- 建议的索引
CREATE INDEX idx_response_school_month ON responses(questionnaire_id, month);
CREATE INDEX idx_answer_teacher_rating ON answers(sso_teacher_id, rating);
CREATE INDEX idx_questionnaire_school ON questionnaires(sso_school_code, month);
```

## 🛡️ 安全考虑

- **输入验证**：严格的参数验证和格式检查
- **SQL注入防护**：使用参数化查询
- **权限控制**：基于SSO的访问权限验证
- **数据脱敏**：敏感信息的适当处理

## 🚀 部署建议

### 生产环境配置

```typescript
// config.prod.ts
export default {
  sequelize: {
    pool: {
      max: 20,
      min: 5,
      idle: 30000,
      acquire: 60000
    },
    logging: false
  },
  redis: {
    host: 'redis-host',
    port: 6379,
    db: 0
  }
}
```

### 监控告警

- **性能监控**：监控SQL查询性能，设置慢查询告警
- **错误监控**：统计接口错误率，异常情况及时告警
- **资源监控**：监控数据库连接数、内存使用等

## 📈 扩展功能

### 1. 高级分析
- **相关性分析**：分析不同评价维度之间的相关性
- **预测分析**：基于历史数据预测未来趋势
- **异常检测**：识别异常评分和评价模式

### 2. 报告生成
- **自动报告**：基于统计数据自动生成分析报告
- **图表导出**：支持将统计图表导出为图片或PDF
- **数据导出**：支持Excel、CSV等格式的数据导出

### 3. 实时分析
- **实时仪表板**：实时展示关键指标
- **推送通知**：重要指标变化时推送通知
- **预警机制**：设置阈值，异常时自动预警

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 文档地址：[Documentation]

---

**🎉 恭喜！教师评价问卷统计分析模块已完成开发并通过测试验证！**
