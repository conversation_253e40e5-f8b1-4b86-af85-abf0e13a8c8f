import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';

describe('test/controller/parent.controller.test.ts', () => {
  let app: any;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('setup', err);
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should POST /api/parent/verify-phone with valid phone', async () => {
    const result = await createHttpRequest(app)
      .post('/api/parent/verify-phone')
      .send({
        phone: '13800138000', // 测试手机号
      });

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('is_valid');
    expect(result.body.data).toHaveProperty('message');
  });

  it('should POST /api/parent/verify-phone with invalid phone format', async () => {
    const result = await createHttpRequest(app)
      .post('/api/parent/verify-phone')
      .send({
        phone: '12345', // 无效格式
      });

    expect(result.status).toBe(422); // 验证失败
  });

  it('should POST /api/parent/verify-phone without phone', async () => {
    const result = await createHttpRequest(app)
      .post('/api/parent/verify-phone')
      .send({});

    expect(result.status).toBe(422); // 验证失败
  });

  it('should GET /api/parent/student-questionnaires with valid params', async () => {
    const result = await createHttpRequest(app)
      .get('/api/parent/student-questionnaires')
      .query({
        sso_school_code: 'school_001',
        sso_student_id: 'student_001',
        month: '2024-01',
        parent_phone: '13800138000',
      });

    expect(result.status).toBe(200);
    expect(result.body.errCode).toBe(0);
    expect(result.body.data).toHaveProperty('has_questionnaire');
    expect(result.body.data).toHaveProperty('message');
  });

  it('should GET /api/parent/student-questionnaires with missing params', async () => {
    const result = await createHttpRequest(app)
      .get('/api/parent/student-questionnaires')
      .query({
        sso_school_code: 'school_001',
        // 缺少必要参数
      });

    expect(result.status).toBe(422); // 验证失败
  });

  it('should GET /api/parent/student-questionnaires with invalid month format', async () => {
    const result = await createHttpRequest(app)
      .get('/api/parent/student-questionnaires')
      .query({
        sso_school_code: 'school_001',
        sso_student_id: 'student_001',
        month: '2024-1', // 无效格式
      });

    expect(result.status).toBe(422); // 验证失败
  });
});
