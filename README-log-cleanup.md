# 日志清理功能

## 功能说明

使用 Midway.js 的数据订阅功能实现每日自动清除超过3个月的操作日志，防止数据库无限增长。

## 实现方式

### 核心文件

- `src/listener/clear-log.ts` - 日志清理监听器
- `src/service/operation-log.service.ts` - 操作日志服务（包含清理方法）

### 工作原理

1. **初始化**: 应用启动时，`ClearLogListener` 自动初始化
2. **计算执行时间**: 计算下次清理时间（每天凌晨2点）
3. **定时执行**: 使用 `setTimeout` 和 `setInterval` 实现每日定时清理
4. **清理逻辑**: 调用 `OperationLogService.cleanupExpiredLogs(90)` 删除超过90天的日志
5. **状态更新**: 实时更新清理状态供查询

### 执行时间

- **首次执行**: 应用启动后，计算到下次凌晨2点的时间间隔后执行
- **后续执行**: 每24小时执行一次
- **数据保留**: 保留最近90天（3个月）的日志数据

## 日志输出

系统会在控制台输出详细的执行日志：

```
日志清理监听器：初始化
日志清理监听器：下次清理时间 2025/6/1 02:00:00
日志清理监听器：启动每日清理任务
日志清理监听器：将在 474 分钟后执行首次清理
日志清理监听器：开始执行日志清理任务
日志清理监听器：清理完成，删除了 1250 条超过90天的日志记录
日志清理监听器：下次清理时间 2025/6/2 02:00:00
```

## 配置说明

目前使用硬编码配置：
- 保留天数：90天（3个月）
- 执行时间：每天凌晨2点
- 执行间隔：24小时

如需修改，可在 `src/listener/clear-log.ts` 中调整相关参数。

## 测试

运行测试验证功能：

```bash
npm test src/test/clear-log.test.ts
```

测试覆盖：
- 监听器初始化
- 下次清理时间计算
- 数据状态查询

## 注意事项

1. **数据安全**: 清理操作不可逆，请确保90天的保留期满足业务需求
2. **性能影响**: 清理在凌晨低峰时段执行，减少对业务的影响
3. **错误处理**: 清理失败会记录错误日志，但不会影响应用运行
4. **资源管理**: 应用关闭时会自动清理定时器资源

## 手动清理

如需手动触发清理，可以通过以下方式：

1. 直接调用服务方法：
```typescript
const operationLogService = await app.getApplicationContext().getAsync(OperationLogService);
const deletedCount = await operationLogService.cleanupExpiredLogs(90);
```

2. 通过监听器获取状态：
```typescript
const clearLogListener = await app.getApplicationContext().getAsync(ClearLogListener);
const status = clearLogListener.getData();
```

## 监控建议

建议在生产环境中：
1. 监控清理任务的执行状态
2. 设置清理失败的告警机制
3. 定期检查日志数据量的增长趋势
4. 根据实际情况调整保留天数
