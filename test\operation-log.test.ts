import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';
import { Application } from '@midwayjs/koa';

describe('test/operation-log.test.ts', () => {
  let app: Application;
  let dbConnected = false;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
      dbConnected = true;
      console.log('✅ 数据库连接成功，运行完整测试');
    } catch (err) {
      console.warn('⚠️ 数据库连接失败，跳过需要数据库的测试:', err.message);
      dbConnected = false;
    }
  }, 15000);

  afterAll(async () => {
    if (app) {
      await close(app);
    }
  });

  it('should POST /api/operation-log - create operation log', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试创建操作日志接口
    const logData = {
      operator_user_id: 'user_001',
      operator_user_name: '张三',
      operator_user_role: 'teacher',
      operator_school_code: 'school_001',
      module: 'questionnaire',
      operation_type: 'create',
      operation_description: '创建问卷',
      target_id: '1',
      target_type: 'questionnaire',
      request_params: JSON.stringify({ title: '测试问卷' }),
      operation_status: 'success',
      ip_address: '127.0.0.1',
      user_agent: 'Mozilla/5.0',
      request_path: '/api/questionnaire',
      request_method: 'POST',
      response_time: 150
    };

    const result = await createHttpRequest(app)
      .post('/api/operation-log')
      .send(logData);

    console.log('创建操作日志结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/operation-log - get operation log list', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取操作日志列表接口
    const result = await createHttpRequest(app)
      .get('/api/operation-log')
      .query({
        operator_user_id: 'user_001',
        module: 'questionnaire',
        page: 1,
        limit: 10
      });

    console.log('获取操作日志列表结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/operation-log/statistics - get operation log statistics', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取操作日志统计接口
    const result = await createHttpRequest(app)
      .get('/api/operation-log/statistics')
      .query({
        operator_school_code: 'school_001',
        granularity: 'day'
      });

    console.log('获取操作日志统计结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/operation-log/user/:userId/history - get user operation history', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取用户操作历史接口
    const result = await createHttpRequest(app)
      .get('/api/operation-log/user/user_001/history')
      .query({
        module: 'questionnaire',
        page: 1,
        limit: 10
      });

    console.log('获取用户操作历史结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/operation-log/school/:schoolId - get school operation logs', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取学校操作日志接口
    const result = await createHttpRequest(app)
      .get('/api/operation-log/school/school_001')
      .query({
        module: 'questionnaire',
        page: 1,
        limit: 10
      });

    console.log('获取学校操作日志结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should GET /api/operation-log/trend - get operation log trend', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试获取操作日志趋势接口
    const result = await createHttpRequest(app)
      .get('/api/operation-log/trend')
      .query({
        operator_school_code: 'school_001',
        granularity: 'day'
      });

    console.log('获取操作日志趋势结果:', result.body);
    expect(result.status).toBe(200);
  });

  it('should POST /api/operation-log/cleanup - cleanup expired logs', async () => {
    if (!dbConnected) {
      console.log('⏭️ 跳过测试：数据库未连接');
      return;
    }

    // 测试清理过期日志接口
    const result = await createHttpRequest(app)
      .post('/api/operation-log/cleanup')
      .send({
        days_to_keep: 30
      });

    console.log('清理过期日志结果:', result.body);
    expect(result.status).toBe(200);
  });

  // 不需要数据库的基础测试
  it('should validate operation log DTO structure', () => {
    // 测试操作日志DTO结构
    const createLogDto = {
      operator_user_id: 'user_001',
      operator_user_name: '张三',
      operator_user_role: 'teacher',
      operator_school_code: 'school_001',
      module: 'questionnaire',
      operation_type: 'create',
      operation_description: '创建问卷',
      target_id: '1',
      target_type: 'questionnaire',
      operation_status: 'success'
    };

    expect(createLogDto.operator_user_id).toBeDefined();
    expect(createLogDto.module).toBeDefined();
    expect(createLogDto.operation_type).toBeDefined();
    expect(createLogDto.operation_description).toBeDefined();
    expect(['questionnaire', 'response', 'statistics', 'system']).toContain(createLogDto.module);
    expect(['create', 'update', 'delete', 'query', 'submit', 'publish', 'close']).toContain(createLogDto.operation_type);
    expect(['success', 'failed', 'pending']).toContain(createLogDto.operation_status);
  });

  it('should validate query operation log DTO structure', () => {
    // 测试查询操作日志DTO结构
    const queryLogDto = {
      operator_user_id: 'user_001',
      operator_school_code: 'school_001',
      module: 'questionnaire',
      operation_type: 'create',
      operation_status: 'success',
      page: 1,
      limit: 20,
      sort_by: 'operation_time',
      sort_order: 'DESC'
    };

    expect(typeof queryLogDto.page).toBe('number');
    expect(typeof queryLogDto.limit).toBe('number');
    expect(queryLogDto.page).toBeGreaterThan(0);
    expect(queryLogDto.limit).toBeGreaterThan(0);
    expect(queryLogDto.limit).toBeLessThanOrEqual(100);
    expect(['operation_time', 'created_at', 'response_time']).toContain(queryLogDto.sort_by);
    expect(['ASC', 'DESC']).toContain(queryLogDto.sort_order);
  });

  it('should validate operation log statistics structure', () => {
    // 测试操作日志统计结构
    const mockStatistics = {
      total_operations: 100,
      success_operations: 95,
      failed_operations: 5,
      success_rate: 95.0,
      avg_response_time: 150.5,
      operation_trend: [
        {
          time_period: '2024-01-01',
          total_count: 20,
          success_count: 19,
          failed_count: 1,
          avg_response_time: 145.2
        }
      ],
      module_distribution: [
        {
          module: 'questionnaire',
          count: 50,
          percentage: 50.0
        }
      ],
      user_activity: [
        {
          operator_user_id: 'user_001',
          operator_user_name: '张三',
          operation_count: 25,
          success_count: 24,
          failed_count: 1,
          last_operation_time: '2024-01-01T10:00:00Z'
        }
      ]
    };

    expect(typeof mockStatistics.total_operations).toBe('number');
    expect(typeof mockStatistics.success_operations).toBe('number');
    expect(typeof mockStatistics.failed_operations).toBe('number');
    expect(typeof mockStatistics.success_rate).toBe('number');
    expect(typeof mockStatistics.avg_response_time).toBe('number');
    expect(Array.isArray(mockStatistics.operation_trend)).toBe(true);
    expect(Array.isArray(mockStatistics.module_distribution)).toBe(true);
    expect(Array.isArray(mockStatistics.user_activity)).toBe(true);

    // 验证成功率计算
    const expectedSuccessRate = (mockStatistics.success_operations / mockStatistics.total_operations) * 100;
    expect(mockStatistics.success_rate).toBe(expectedSuccessRate);

    // 验证总数一致性
    expect(mockStatistics.success_operations + mockStatistics.failed_operations).toBe(mockStatistics.total_operations);
  });

  it('should validate operation module and type enums', () => {
    // 测试操作模块枚举
    const validModules = ['questionnaire', 'response', 'statistics', 'system'];
    const validOperationTypes = ['create', 'update', 'delete', 'query', 'submit', 'publish', 'close'];
    const validStatuses = ['success', 'failed', 'pending'];

    validModules.forEach(module => {
      expect(typeof module).toBe('string');
      expect(module.length).toBeGreaterThan(0);
    });

    validOperationTypes.forEach(type => {
      expect(typeof type).toBe('string');
      expect(type.length).toBeGreaterThan(0);
    });

    validStatuses.forEach(status => {
      expect(typeof status).toBe('string');
      expect(status.length).toBeGreaterThan(0);
    });
  });

  it('should validate IP address and user agent format', () => {
    // 测试IP地址和用户代理格式
    const mockLogData = {
      ip_address: '***********',
      user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      request_path: '/api/questionnaire',
      request_method: 'POST',
      response_time: 150
    };

    // IP地址格式验证（简单验证）
    expect(mockLogData.ip_address).toMatch(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/);
    
    // 用户代理不为空
    expect(mockLogData.user_agent.length).toBeGreaterThan(0);
    
    // 请求路径以/开头
    expect(mockLogData.request_path).toMatch(/^\//);
    
    // 请求方法为有效的HTTP方法
    expect(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).toContain(mockLogData.request_method);
    
    // 响应时间为正数
    expect(mockLogData.response_time).toBeGreaterThan(0);
  });
});
