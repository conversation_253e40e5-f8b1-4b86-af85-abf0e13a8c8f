import { Rule, RuleType } from '@midwayjs/validate';
import { ParentInfo } from '../service/api_sso/interface';

/**
 * 验证家长手机号DTO
 */
export class VerifyParentPhoneDTO {
  @Rule(
    RuleType.string()
      .required()
      .pattern(/^1[3-9]\d{9}$/)
      .messages({
        'string.empty': '家长手机号不能为空',
        'string.pattern.base': '家长手机号格式不正确',
        'any.required': '家长手机号是必填项',
      })
  )
  phone: string;
}

/**
 * 家长手机号验证响应接口
 */
export interface IParentPhoneVerifyResponse {
  is_valid: boolean;
  message: string;
  parent?: ParentInfo;
}
