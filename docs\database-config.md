# 数据库配置优先级说明

## 配置文件优先级

Midway框架会根据 `NODE_ENV` 环境变量自动加载对应的配置文件，优先级如下：

### 1. 开发环境 (NODE_ENV=local)
```
config.default.ts (基础配置)
↓
config.local.ts (覆盖基础配置)
```

### 2. 测试环境 (NODE_ENV=unittest)
```
config.default.ts (基础配置)
↓
config.local.ts (覆盖基础配置)
↓
config.unittest.ts (覆盖前面的配置)
```

### 3. 生产环境 (NODE_ENV=production)
```
config.default.ts (基础配置)
↓
config.prod.ts (覆盖基础配置)
```

## 当前配置说明

### config.default.ts
- **用途**: 基础配置，包含默认的数据库连接信息
- **数据库**: 本地MySQL (127.0.0.1:3306)
- **数据库名**: teacher-evaluation-question

### config.local.ts
- **用途**: 本地开发环境配置
- **数据库**: 远程MySQL (*************:3306)
- **数据库名**: teacher-evaluation-question
- **特点**: 包含sync配置，支持表结构同步

### config.unittest.ts
- **用途**: 单元测试环境配置
- **数据库**: 远程MySQL (*************:3306)
- **数据库名**: teacher-evaluation-question-test (测试专用数据库)
- **特点**: 
  - 使用独立的测试数据库
  - 连接池配置较小
  - 支持表结构修改 (alter: true)

## 测试时的数据库连接策略

### 优先级顺序
1. **首选**: 使用 `config.unittest.ts` 中的远程MySQL配置
2. **备选**: 如果远程数据库不可用，测试会优雅地跳过需要数据库的测试用例
3. **基础测试**: 始终运行不需要数据库的基础验证测试

### 测试配置特点
```typescript
// config.unittest.ts
{
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '*************',        // 使用config.local中的远程地址
        database: 'teacher-evaluation-question-test', // 独立测试数据库
        pool: {
          max: 2,                     // 减少连接数
          acquire: 10000,             // 连接超时
        },
        sync: {
          force: false,               // 不强制重建表
          alter: true,                // 允许修改表结构
        }
      }
    }
  }
}
```

## 运行测试

### 使用远程数据库测试
```bash
npm test
```

### 查看测试输出
- ✅ 数据库连接成功：运行完整测试
- ⚠️ 数据库连接失败：跳过数据库相关测试，只运行基础验证

### 环境变量控制
```bash
# 强制使用内存数据库（需要安装sqlite3）
TEST_DB_TYPE=memory npm test

# CI环境自动使用内存数据库
CI=true npm test
```

## 配置最佳实践

### 1. 开发环境
- 使用 `config.local.ts` 配置本地或远程开发数据库
- 启用 `sync: { alter: true }` 支持表结构同步

### 2. 测试环境
- 使用独立的测试数据库
- 配置较小的连接池
- 启用表结构修改但不强制重建

### 3. 生产环境
- 创建 `config.prod.ts` 配置生产数据库
- 禁用 `sync` 配置
- 配置适当的连接池大小

## 故障排除

### 数据库连接失败
1. 检查网络连接
2. 验证数据库服务是否运行
3. 确认用户名密码正确
4. 检查防火墙设置

### 测试失败
1. 确认测试数据库存在
2. 检查数据库权限
3. 查看测试日志输出
4. 验证表结构是否正确

## 配置示例

### 添加新环境配置
```typescript
// config.staging.ts
export default {
  sequelize: {
    dataSource: {
      default: {
        host: 'staging-db-host',
        database: 'teacher-evaluation-question-staging',
        // 其他配置...
      }
    }
  }
} as MidwayConfig;
```

### 多数据源配置
```typescript
export default {
  sequelize: {
    dataSource: {
      default: {
        // 主数据库配置
      },
      readonly: {
        // 只读数据库配置
      }
    }
  }
} as MidwayConfig;
```
