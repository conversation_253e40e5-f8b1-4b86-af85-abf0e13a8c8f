# 问卷填写功能模块

## 概述

本模块实现了教师评价问卷系统的问卷填写功能，支持家长为学生提交教师评价，包含完整的数据验证、重复提交防护、星级评分转换等功能。

## 功能特性

- ✅ 问卷响应提交
- ✅ 唯一排重键防重复提交
- ✅ SSO学生ID和教师ID验证
- ✅ 星级评分转换（5星/10星制）
- ✅ 总平均分自动计算
- ✅ 批量保存响应和答案记录
- ✅ 响应列表查询和统计
- ✅ 事务保证数据一致性

## 数据模型

### 响应实体 (Response)

```typescript
{
  id: number;                    // 响应ID
  questionnaire_id: number;      // 问卷ID
  parent_phone: string;          // 家长手机号
  parent_name?: string;          // 家长姓名
  sso_student_id: string;        // SSO学生ID
  sso_student_name?: string;     // SSO学生姓名
  sso_student_class?: string;    // SSO学生班级
  sso_student_grade?: string;    // SSO学生年级
  month: string;                 // 月份 (YYYY-MM)
  school_rating?: number;        // 学校评分
  school_description?: string;   // 学校评价描述
  total_average_score?: number;  // 总平均分
  teacher_count: number;         // 评价教师数量
  is_completed: boolean;         // 是否完成
  // ... 其他字段
}
```

### 答案实体 (Answer)

```typescript
{
  id: number;                           // 答案ID
  response_id: number;                  // 响应ID
  sso_teacher_id: string;               // SSO教师ID
  sso_teacher_name?: string;            // SSO教师姓名
  sso_teacher_subject?: string;         // SSO教师科目
  sso_teacher_position?: string;        // SSO教师职位
  sso_teacher_department?: string;      // SSO教师部门
  rating: number;                       // 评分
  description?: string;                 // 评价描述
  tags?: string[];                      // 评价标签
  is_recommended?: boolean;             // 是否推荐
  teaching_quality_rating?: number;     // 教学质量评分
  teaching_attitude_rating?: number;    // 教学态度评分
  classroom_management_rating?: number; // 课堂管理评分
  communication_rating?: number;        // 沟通能力评分
  professional_knowledge_rating?: number; // 专业知识评分
  // ... 其他字段
}
```

## API 接口

### 1. 提交问卷响应

**POST** `/api/response`

**请求体：**
```json
{
  "questionnaire_id": 1,
  "parent_phone": "13800138000",
  "parent_name": "张三家长",
  "sso_student_id": "student_001",
  "month": "2024-01",
  "school_rating": 5,
  "school_description": "学校整体表现很好",
  "teacher_evaluations": [
    {
      "sso_teacher_id": "teacher_001",
      "rating": 5,
      "description": "老师教学认真负责",
      "tags": ["认真", "负责", "专业"],
      "is_recommended": true,
      "teaching_quality_rating": 5,
      "teaching_attitude_rating": 5,
      "classroom_management_rating": 4,
      "communication_rating": 5,
      "professional_knowledge_rating": 5,
      "improvement_suggestions": "希望能多一些互动",
      "most_satisfied_aspect": "教学方法很好",
      "needs_improvement_aspect": "课堂互动可以更多"
    }
  ],
  "remarks": "整体评价很好"
}
```

**响应：**
```json
{
  "errCode": 0,
  "msg": "问卷提交成功",
  "data": {
    "response_id": 1,
    "questionnaire_id": 1,
    "submission_time": "2024-01-15T10:30:00Z",
    "total_average_score": 92.5,
    "teacher_count": 2
  }
}
```

### 2. 获取响应列表

**GET** `/api/response`

**查询参数：**
- `questionnaire_id`: 问卷ID（可选）
- `parent_phone`: 家长手机号（可选）
- `sso_student_id`: 学生ID（可选）
- `month`: 月份 YYYY-MM（可选）
- `is_completed`: 是否完成（可选）
- `page`: 页码，默认1
- `limit`: 每页数量，默认10

### 3. 检查是否已提交响应

**GET** `/api/response/check`

**查询参数：**
- `parent_phone`: 家长手机号（必填）
- `questionnaire_id`: 问卷ID（必填）
- `sso_student_id`: 学生ID（必填）
- `month`: 月份（必填）

### 4. 获取问卷统计信息

**GET** `/api/response/statistics/:questionnaireId`

### 5. 获取评分转换信息

**GET** `/api/response/rating-info/:questionnaireId`

## 核心业务逻辑

### 1. 唯一排重键生成

```typescript
private generateUniqueKey(
  parentPhone: string,
  questionnaireId: number,
  studentId: string,
  month: string
): string {
  return `${parentPhone}_${questionnaireId}_${studentId}_${month}`;
}
```

**规则：** 家长手机号 + 问卷ID + 学生ID + 月份组成唯一键，防止重复提交。

### 2. 星级评分转换

```typescript
private convertRatingToScore(rating: number, starMode: StarMode): number {
  if (starMode === StarMode.FIVE_STAR) {
    // 5星模式：1星=20分，2星=40分，3星=60分，4星=80分，5星=100分
    return rating * 20;
  } else {
    // 10星模式：1星=10分，2星=20分，...，10星=100分
    return rating * 10;
  }
}
```

**转换规则：**
- **5星制**：1星=20分, 2星=40分, 3星=60分, 4星=80分, 5星=100分
- **10星制**：1星=10分, 2星=20分, ..., 10星=100分

### 3. 总平均分计算

```typescript
private calculateTotalAverageScore(teacherEvaluations: any[], starMode: StarMode): number {
  if (!teacherEvaluations || teacherEvaluations.length === 0) {
    return 0;
  }

  const totalScore = teacherEvaluations.reduce((sum, evaluation) => {
    return sum + this.convertRatingToScore(evaluation.rating, starMode);
  }, 0);

  return Math.round((totalScore / teacherEvaluations.length) * 100) / 100; // 保留两位小数
}
```

**计算逻辑：**
1. 将每个教师的星级评分转换为百分制分数
2. 计算所有教师评分的平均值
3. 保留两位小数

### 4. SSO验证

```typescript
// 验证学生ID
private async validateSSOStudentId(ssoStudentId: string): Promise<ISSoStudentInfo>

// 验证教师ID
private async validateSSOTeacherId(ssoTeacherId: string): Promise<ISSoTeacherInfo>
```

**验证内容：**
- 学生/教师ID是否存在
- 学生/教师状态是否为活跃
- 获取完整的学生/教师信息

## 数据验证规则

### 提交响应验证

1. **问卷验证**
   - 问卷必须存在且状态为已发布
   - 当前时间在问卷有效期内
   - 提交月份与问卷月份匹配

2. **重复提交检查**
   - 基于唯一键检查是否已提交
   - 同一家长不能为同一学生在同一月份重复提交

3. **SSO验证**
   - 学生ID必须有效且状态正常
   - 所有教师ID必须有效且状态正常

4. **评分验证**
   - 评分范围必须在1到问卷星级模式之间
   - 至少评价一位教师
   - 不能超过问卷设置的最大教师数量限制

### 输入参数验证

```typescript
// 家长手机号验证
@Rule(RuleType.string().required().pattern(/^1[3-9]\d{9}$/))
parent_phone: string;

// 月份格式验证
@Rule(RuleType.string().required().pattern(/^\d{4}-\d{2}$/))
month: string;

// 评分范围验证
@Rule(RuleType.number().integer().min(1).max(10).required())
rating: number;

// 教师评价数组验证
@Rule(RuleType.array().min(1).required())
teacher_evaluations: TeacherEvaluationDTO[];
```

## 事务处理

所有数据库操作都在事务中执行，确保数据一致性：

```typescript
const transaction: Transaction = await this.sequelize.transaction();

try {
  // 1. 验证问卷
  // 2. 检查重复提交
  // 3. 验证SSO信息
  // 4. 创建响应记录
  // 5. 批量创建答案记录
  
  await transaction.commit();
} catch (error) {
  await transaction.rollback();
  throw error;
}
```

## 错误处理

### 常见错误类型

1. **业务错误**
   - 问卷不存在或未发布
   - 重复提交
   - SSO验证失败
   - 评分超出范围

2. **参数错误**
   - 手机号格式错误
   - 月份格式错误
   - 必填参数缺失

3. **系统错误**
   - 数据库连接失败
   - 事务回滚

### 错误响应格式

```json
{
  "errCode": 400,
  "msg": "该家长已为此学生在本月提交过问卷，请勿重复提交",
  "data": null
}
```

## 使用示例

### 前端提交问卷

```javascript
const submitResponse = async (responseData) => {
  try {
    const response = await fetch('/api/response', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(responseData)
    });
    
    const result = await response.json();
    
    if (result.errCode === 0) {
      console.log('提交成功:', result.data);
    } else {
      console.error('提交失败:', result.msg);
    }
  } catch (error) {
    console.error('网络错误:', error);
  }
};
```

### 检查重复提交

```javascript
const checkExists = async (checkData) => {
  const params = new URLSearchParams(checkData);
  const response = await fetch(`/api/response/check?${params}`);
  const result = await response.json();
  
  return result.data.exists;
};
```

## 性能优化

1. **批量操作**：使用 `bulkCreate` 批量创建答案记录
2. **索引优化**：在关键字段上建立索引
3. **事务优化**：合理控制事务范围
4. **查询优化**：使用 `include` 进行关联查询

## 安全考虑

1. **输入验证**：严格的参数验证和格式检查
2. **重复提交防护**：基于唯一键的重复检查
3. **SSO验证**：确保学生和教师ID的有效性
4. **IP记录**：记录提交IP用于审计
5. **事务保护**：确保数据一致性
