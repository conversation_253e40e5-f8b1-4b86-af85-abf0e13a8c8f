import { Provide, Inject } from '@midwayjs/core';
import { Custome } from './api_sso/custome.service';
import { IParentPhoneVerifyResponse } from '../dto/parent.dto';
import { CustomError } from '../error/custom.error';

@Provide()
export class ParentService {
  @Inject()
  custome: Custome;

  /**
   * 验证家长手机号
   * 通过查询该手机号下的学生信息来验证手机号是否有效
   * @param phone 家长手机号
   * @returns 验证结果
   */
  async verifyParentPhone(phone: string): Promise<IParentPhoneVerifyResponse> {
    try {
      // 调用SSO接口获取该手机号下的学生信息
      const parent = await this.custome.getChildren(phone);

      if (!parent) {
        return {
          is_valid: false,
          message: '手机号验证失败，请确认手机号是否正确',
        };
      }

      // // 格式化学生信息
      // const formattedChildren = children.map(child => ({
      //   id: child.id || child.studentId || '',
      //   name: child.name || child.studentName || '',
      //   class: child.class || child.className || '',
      //   grade: child.grade || child.gradeName || '',
      //   school_code: child.school_code || child.schoolCode || '',
      // }));

      return {
        is_valid: true,
        message: '验证成功',
        parent,
      };
    } catch (error) {
      // 如果是网络错误或SSO接口错误，返回验证失败
      if (error.message && error.message.includes('网络')) {
        throw new CustomError('网络连接异常，请稍后重试');
      }

      // 其他错误也认为是验证失败
      return {
        is_valid: false,
        message: '手机号验证失败，请确认手机号是否正确',
      };
    }
  }
}
