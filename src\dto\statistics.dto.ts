import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 学校统计查询DTO
 */
export class SchoolStatisticsQueryDTO {
  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO学校编码不能为空',
      'string.max': 'SSO学校编码不能超过50个字符',
      'any.required': 'SSO学校编码是必填项',
    })
  )
  sso_school_code: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '月份格式必须为YYYY-MM',
      })
  )
  month?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '开始月份格式必须为YYYY-MM',
      })
  )
  start_month?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '结束月份格式必须为YYYY-MM',
      })
  )
  end_month?: string;

  @Rule(RuleType.boolean().optional())
  include_trend?: boolean;

  @Rule(RuleType.boolean().optional())
  include_teacher_ranking?: boolean;
}

/**
 * 教师统计查询DTO
 */
export class TeacherStatisticsQueryDTO {
  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO教师ID不能为空',
      'string.max': 'SSO教师ID不能超过50个字符',
      'any.required': 'SSO教师ID是必填项',
    })
  )
  sso_teacher_id: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': 'SSO学校编码不能超过50个字符',
    })
  )
  sso_school_code?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '月份格式必须为YYYY-MM',
      })
  )
  month?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '开始月份格式必须为YYYY-MM',
      })
  )
  start_month?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '结束月份格式必须为YYYY-MM',
      })
  )
  end_month?: string;

  @Rule(RuleType.boolean().optional())
  include_distribution?: boolean;

  @Rule(RuleType.boolean().optional())
  include_keywords?: boolean;

  @Rule(RuleType.boolean().optional())
  include_trend?: boolean;
}

/**
 * 教师排名查询DTO
 */
export class TeacherRankingQueryDTO {
  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO学校编码不能为空',
      'string.max': 'SSO学校编码不能超过50个字符',
      'any.required': 'SSO学校编码是必填项',
    })
  )
  sso_school_code: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '月份格式必须为YYYY-MM',
      })
  )
  month?: string;

  @Rule(
    RuleType.string().optional().max(100).messages({
      'string.max': '科目名称不能超过100个字符',
    })
  )
  subject?: string;

  @Rule(
    RuleType.string().optional().max(100).messages({
      'string.max': '部门名称不能超过100个字符',
    })
  )
  department?: string;

  @Rule(
    RuleType.number().integer().min(1).default(1).messages({
      'number.min': '页码不能小于1',
      'number.integer': '页码必须是整数',
    })
  )
  page?: number;

  @Rule(
    RuleType.number().integer().min(1).max(100).default(20).messages({
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100',
      'number.integer': '每页数量必须是整数',
    })
  )
  limit?: number;

  @Rule(
    RuleType.string()
      .valid('average_score', 'evaluation_count', 'recommendation_rate')
      .default('average_score')
      .messages({
        'any.only':
          '排序字段只能是average_score、evaluation_count或recommendation_rate',
      })
  )
  sort_by?: string;

  @Rule(
    RuleType.string().valid('ASC', 'DESC').default('DESC').messages({
      'any.only': '排序方向只能是ASC或DESC',
    })
  )
  sort_order?: string;
}

/**
 * 趋势分析查询DTO
 */
export class TrendAnalysisQueryDTO {
  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO学校编码不能为空',
      'string.max': 'SSO学校编码不能超过50个字符',
      'any.required': 'SSO学校编码是必填项',
    })
  )
  sso_school_code: string;

  @Rule(
    RuleType.string()
      .required()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.empty': '开始月份不能为空',
        'string.pattern.base': '开始月份格式必须为YYYY-MM',
        'any.required': '开始月份是必填项',
      })
  )
  start_month: string;

  @Rule(
    RuleType.string()
      .required()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.empty': '结束月份不能为空',
        'string.pattern.base': '结束月份格式必须为YYYY-MM',
        'any.required': '结束月份是必填项',
      })
  )
  end_month: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': 'SSO教师ID不能超过50个字符',
    })
  )
  sso_teacher_id?: string;

  @Rule(
    RuleType.string().valid('school', 'teacher').default('school').messages({
      'any.only': '分析类型只能是school或teacher',
    })
  )
  analysis_type?: string;
}
