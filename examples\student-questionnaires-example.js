/**
 * 学生问卷查询接口使用示例
 * 
 * 这个示例展示了如何使用 /api/parent/student-questionnaires 接口
 * 查询学生是否有可填写的问卷
 */

const BASE_URL = 'http://localhost:7001';

/**
 * 查询学生问卷
 * @param {string} ssoSchoolCode 学校编码
 * @param {string} ssoStudentId 学生ID
 * @param {string} month 月份 (YYYY-MM)
 * @param {string} parentPhone 家长手机号 (可选)
 */
async function getStudentQuestionnaires(ssoSchoolCode, ssoStudentId, month, parentPhone) {
  try {
    const params = new URLSearchParams({
      sso_school_code: ssoSchoolCode,
      sso_student_id: ssoStudentId,
      month: month,
    });

    // 如果提供了家长手机号，添加到查询参数中
    if (parentPhone) {
      params.append('parent_phone', parentPhone);
    }

    const response = await fetch(`${BASE_URL}/api/parent/student-questionnaires?${params}`);
    const result = await response.json();

    if (result.errCode === 0) {
      console.log('✅ 查询成功');
      
      if (result.data.has_questionnaire) {
        console.log('📋 找到问卷：', result.data.questionnaire.title);
        console.log('📅 问卷月份：', result.data.questionnaire.month);
        console.log('⭐ 星级模式：', result.data.questionnaire.star_mode + '星制');
        console.log('🏫 学校：', result.data.questionnaire.sso_school_name || result.data.questionnaire.sso_school_code);
        
        if (result.data.questionnaire.is_submitted) {
          console.log('✅ 状态：已提交');
        } else {
          console.log('⏳ 状态：待填写');
        }
        
        if (result.data.questionnaire.max_teachers_limit > 0) {
          console.log('👥 最多评价教师数：', result.data.questionnaire.max_teachers_limit);
        }
        
        if (result.data.questionnaire.instructions) {
          console.log('📝 填写说明：', result.data.questionnaire.instructions);
        }
      } else {
        console.log('❌ 暂无可填写的问卷');
        console.log('💬 说明：', result.data.message);
      }
    } else {
      console.error('❌ 查询失败：', result.msg);
    }

    return result;
  } catch (error) {
    console.error('❌ 网络错误：', error.message);
    throw error;
  }
}

/**
 * 批量查询多个学生的问卷
 */
async function batchQueryStudentQuestionnaires() {
  const students = [
    { schoolCode: 'school_001', studentId: 'student_001', month: '2024-01' },
    { schoolCode: 'school_001', studentId: 'student_002', month: '2024-01' },
    { schoolCode: 'school_002', studentId: 'student_003', month: '2024-01' },
  ];

  console.log('🔍 开始批量查询学生问卷...\n');

  for (const student of students) {
    console.log(`查询学生 ${student.studentId} 在 ${student.schoolCode} 的 ${student.month} 月问卷：`);
    await getStudentQuestionnaires(student.schoolCode, student.studentId, student.month);
    console.log('---');
  }
}

/**
 * 示例：检查特定家长的学生是否有问卷
 */
async function checkParentStudentQuestionnaires() {
  const parentPhone = '13800138000';
  const schoolCode = 'school_001';
  const studentId = 'student_001';
  const month = '2024-01';

  console.log(`🔍 检查家长 ${parentPhone} 的学生 ${studentId} 是否有问卷可填写...\n`);

  const result = await getStudentQuestionnaires(schoolCode, studentId, month, parentPhone);
  
  if (result.errCode === 0 && result.data.has_questionnaire) {
    if (result.data.questionnaire.is_submitted) {
      console.log('✅ 该家长已为此学生提交过问卷');
    } else {
      console.log('⏳ 该家长可以为此学生填写问卷');
      console.log('🔗 问卷ID：', result.data.questionnaire.id);
    }
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  console.log('🚀 学生问卷查询接口示例\n');
  
  // 示例1：查询单个学生问卷
  getStudentQuestionnaires('school_001', 'student_001', '2024-01')
    .then(() => console.log('\n'))
    .then(() => {
      // 示例2：查询时包含家长手机号
      return getStudentQuestionnaires('school_001', 'student_001', '2024-01', '13800138000');
    })
    .then(() => console.log('\n'))
    .then(() => {
      // 示例3：批量查询
      return batchQueryStudentQuestionnaires();
    })
    .then(() => {
      // 示例4：检查家长学生问卷状态
      return checkParentStudentQuestionnaires();
    })
    .catch(error => {
      console.error('示例执行失败：', error);
    });
}

module.exports = {
  getStudentQuestionnaires,
  batchQueryStudentQuestionnaires,
  checkParentStudentQuestionnaires,
};
