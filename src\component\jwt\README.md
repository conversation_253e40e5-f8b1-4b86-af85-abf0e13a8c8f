# JWT认证组件

基于MidwayJS框架的JWT认证组件，提供完整的JWT认证功能，支持与SSO服务集成，不需要维护自己的用户信息。

## 🚀 核心特性

- **🔧 即插即用**：引入组件即可获得完整JWT认证功能，无需手动配置
- **📦 依赖自管理**：自动管理 `@midwayjs/jwt` 和 `@midwayjs/axios` 依赖
- **🔄 自动中间件注册**：自动注册JWT认证中间件，无需手动添加
- **🔐 无状态认证**：基于JWT的无状态认证，不需要在服务端存储会话信息
- **⚡ 自动令牌刷新**：当令牌即将过期时，自动刷新令牌
- **⚙️ 高度可配置**：通过配置文件灵活配置认证行为
- **🌐 SSO集成**：直接与SSO服务集成，获取用户信息
- **📋 完整API**：提供登录、刷新令牌、获取用户信息、退出登录等完整API
- **🎯 完全自包含**：组件内部包含所有必要的工具类和错误类，不依赖项目公共代码
- **📐 统一响应格式**：所有接口都返回统一的响应格式，便于前端处理

## 功能特点

- **组件化设计**：完全封装的组件，符合MidwayJS组件化设计理念
- **零配置启动**：最少配置即可启动，支持渐进式配置
- **内置API服务**：内置API管理器和认证服务，只需配置认证中心URL即可使用
- **智能白名单**：支持路径白名单配置，灵活控制认证范围

## 安装与使用

### 1. 复制组件到项目中

将`src/component/jwt`目录复制到您的项目中。

### 2. 在项目中引入组件

在项目的`configuration.ts`中导入并使用组件：

```typescript
import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
// 导入JWT认证组件
import jwtComponent from './component/jwt-component';

@Configuration({
  imports: [
    koa,
    validate,
    jwtComponent, // 引入JWT认证组件（自动管理jwt和axios依赖）
    // 其他组件...
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    // 组件会自动注册JWT认证中间件，无需手动配置
    // 其他中间件配置...
  }
}
```

**注意**：
- ✅ **无需手动引入依赖**：组件自动管理 `@midwayjs/jwt` 和 `@midwayjs/axios` 依赖
- ✅ **无需手动注册中间件**：组件自动注册JWT认证中间件
- ✅ **即插即用**：只需引入组件即可获得完整的JWT认证功能

### 3. 配置组件

在项目的`config/config.default.ts`中添加JWT认证组件的配置：

```typescript
import { MidwayConfig } from '@midwayjs/core';

export default {
  // JWT配置（必需）
  jwt: {
    secret: 'your-jwt-secret',
    expiresIn: '2h', // 访问令牌过期时间
  },

  // 令牌配置（必需）
  token: {
    secret: 'your-token-secret',
    expiresIn: 2 * 3600, // 访问令牌过期时间（秒）
    refreshExpiresIn: 15 * 24 * 3600, // 刷新令牌过期时间（秒）
  },

  // JWT认证组件配置
  jwtAuth: {
    enable: true,                 // 是否启用
    whitelist: [                  // 白名单路径
      '/auth/login',
      '/auth/register',
      '/auth/refresh-token',
      '/public',
      // 其他不需要认证的路径...
    ],
    refreshThreshold: 30 * 60,    // 令牌刷新阈值（秒）

    // API管理器配置
    apiManagerBaseURL: 'http://sso.example.com', // SSO服务基础URL
    apiPath: '/apis/request',                    // API请求路径
    postByUserCodesApiCode: 'postByUserCodes',   // 获取用户信息的API编码
    getDataConverApiCode: 'getDataConver',       // 解密信息的API编码
    apiTimeout: 10000,                           // API请求超时时间（毫秒）
  },

  // 其他配置...
} as MidwayConfig;
```

### 4. 安装依赖

确保项目中安装了必要的依赖：

```bash
npm install @midwayjs/jwt @midwayjs/axios jsonwebtoken --save
npm install @types/jsonwebtoken --save-dev
```

**说明**：
- `@midwayjs/jwt`：MidwayJS的JWT组件
- `@midwayjs/axios`：MidwayJS的HTTP客户端组件
- `jsonwebtoken`：JWT令牌处理库
- `@types/jsonwebtoken`：TypeScript类型定义

组件已经内置了API服务，不需要额外实现用户信息服务。只需在配置文件中设置SSO服务的URL和API编码即可。

## 🛠️ 组件内部工具

JWT组件完全自包含，提供以下内部工具类：

### 响应工具类 (JwtResponseUtil)
```typescript
import { JwtResponseUtil } from './component/jwt/common/response';

// 成功响应
JwtResponseUtil.success(data, '操作成功');

// 错误响应
JwtResponseUtil.error(401, '认证失败');

// 分页响应
JwtResponseUtil.page(list, total, page, limit);
```

### 错误类 (JwtBusinessError系列)
```typescript
import {
  JwtCustomError,
  JwtAuthError,
  JwtParamError
} from './component/jwt/common/error';

// 通用业务错误
throw new JwtCustomError('业务错误', 400);

// 认证错误
throw new JwtAuthError('认证失败');

// 参数错误
throw new JwtParamError('参数无效');
```

## 快速开始

### 最简配置

如果您只需要基本的JWT认证功能，最简配置如下：

1. **引入组件**（在 `configuration.ts` 中）：
```typescript
import jwtComponent from './component/jwt-component';

@Configuration({
  imports: [
    // ... 其他组件
    jwtComponent, // 引入JWT认证组件
  ],
})
```

2. **基础配置**（在 `config/config.default.ts` 中）：
```typescript
export default {
  // JWT配置（必需）
  jwt: {
    secret: 'your-jwt-secret',
    expiresIn: '2h',
  },

  // JWT认证组件配置
  jwtAuth: {
    enable: true,
    apiManagerBaseURL: 'http://your-sso-server.com',
  },
} as MidwayConfig;
```

3. **在控制器中使用**：
```typescript
import { BaseController } from '../common/BaseController';

@Controller('/api/protected')
export class ProtectedController extends BaseController {
  @Get('/')
  async getProtectedData() {
    // 自动获取当前用户信息
    const { userId, userName } = this.getCurrentUser();
    return { message: `Hello ${userName}!`, userId };
  }
}
```

就这么简单！组件会自动处理JWT认证、中间件注册等所有细节。

## 使用示例

### 登录

```typescript
// 前端代码
async function login() {
  const response = await fetch('/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      code: 'your-code',
    }),
  });

  const result = await response.json();
  if (result.errCode === 0) {
    // 保存令牌
    localStorage.setItem('accessToken', result.data.accessToken);
    localStorage.setItem('refreshToken', result.data.refreshToken);
    // 保存用户信息
    localStorage.setItem('userInfo', JSON.stringify(result.data.userInfo));
    // 跳转到首页
    window.location.href = '/';
  } else {
    alert('登录失败：' + result.msg);
  }
}
```

### 使用访问令牌

```typescript
// 前端代码
async function fetchData() {
  const accessToken = localStorage.getItem('accessToken');

  const response = await fetch('/api/data', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  // 检查是否有新令牌
  const newToken = response.headers.get('New-Token');
  if (newToken) {
    localStorage.setItem('accessToken', newToken);
  }

  return await response.json();
}
```

### 刷新令牌

```typescript
// 前端代码
async function refreshToken() {
  const refreshToken = localStorage.getItem('refreshToken');

  const response = await fetch('/auth/refresh-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      refreshToken,
    }),
  });

  const result = await response.json();
  if (result.errCode === 0) {
    // 更新令牌
    localStorage.setItem('accessToken', result.data.accessToken);
    localStorage.setItem('refreshToken', result.data.refreshToken);
    return true;
  } else {
    // 刷新失败，需要重新登录
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('userInfo');
    window.location.href = '/login';
    return false;
  }
}
```

### 获取当前用户信息

```typescript
// 前端代码
async function getCurrentUser() {
  const accessToken = localStorage.getItem('accessToken');

  const response = await fetch('/auth/current-user', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  // 检查是否有新令牌
  const newToken = response.headers.get('New-Token');
  if (newToken) {
    localStorage.setItem('accessToken', newToken);
  }

  return await response.json();
}
```

### 退出登录

```typescript
// 前端代码
async function logout() {
  const accessToken = localStorage.getItem('accessToken');

  await fetch('/auth/logout', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  // 清除本地存储的令牌和用户信息
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('userInfo');

  // 跳转到登录页
  window.location.href = '/login';
}
```

## API参考

### 登录

- **URL**: `/auth/login`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "enterpriseCode": "企业编码",
    "userCode": "用户编码"
  }
  ```
- **响应**:
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "accessToken": "JWT访问令牌",
      "refreshToken": "刷新令牌",
      "userInfo": {
        "id": "用户ID",
        "userCode": "用户编码",
        "username": "用户名",
        "realName": "真实姓名",
        "enterpriseCode": "企业编码",
        "enterpriseName": "企业名称",
        "roles": ["角色1", "角色2"],
        "mobile": "手机号",
        "email": "邮箱"
      }
    }
  }
  ```

### 刷新令牌

- **URL**: `/auth/refresh-token`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "refreshToken": "刷新令牌"
  }
  ```
- **响应**:
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "accessToken": "新的JWT访问令牌",
      "refreshToken": "新的刷新令牌"
    }
  }
  ```

### 获取当前用户信息

- **URL**: `/auth/current-user`
- **方法**: `GET`
- **请求头**: `Authorization: Bearer 访问令牌`
- **响应**:
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "id": "用户ID",
      "userCode": "用户编码",
      "username": "用户名",
      "realName": "真实姓名",
      "enterpriseCode": "企业编码",
      "enterpriseName": "企业名称",
      "roles": ["角色1", "角色2"],
      "mobile": "手机号",
      "email": "邮箱"
    }
  }
  ```

### 退出登录

- **URL**: `/auth/logout`
- **方法**: `POST`
- **请求头**: `Authorization: Bearer 访问令牌`
- **响应**:
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "success": true
    }
  }
  ```

## 注意事项

1. **依赖管理**：组件自动管理 `@midwayjs/jwt` 和 `@midwayjs/axios` 依赖，无需在主配置中手动引入
2. **中间件注册**：组件自动注册JWT认证中间件，无需手动配置
3. **配置要求**：确保配置了正确的SSO服务URL和API编码
4. **前端集成**：
   - 前端需要妥善保存令牌，并在请求中携带
   - 当收到新令牌时，前端需要更新本地存储的令牌
   - 当令牌刷新失败时，前端需要引导用户重新登录
5. **白名单配置**：默认白名单包括 `/auth/*`、`/public/*` 等路径，可通过配置自定义
6. **组件启用**：通过 `jwtAuth.enable` 配置控制组件是否启用，默认为 `true`

## 组件结构

```
src/component/jwt/
├── common/                  # 公共工具
│   ├── api-manager.ts       # API管理器
│   └── logger.ts            # 日志工具
├── config/                  # 配置文件
│   └── config.default.ts    # 默认配置
├── controller/              # 控制器
│   └── jwt.controller.ts    # JWT认证控制器
├── middleware/              # 中间件
│   └── jwt.middleware.ts    # JWT认证中间件
├── service/                 # 服务
│   ├── auth.service.ts      # 认证API服务
│   └── jwt.service.ts       # JWT认证服务
├── index.ts                 # 组件入口
├── interface.ts             # 接口定义
└── README.md                # 组件文档
```
