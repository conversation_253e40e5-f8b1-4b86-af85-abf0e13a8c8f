/*
 * @Description: JWT认证中间件
 * @Date: 2025-05-15
 */
import { Config, Inject, Middleware, IMiddleware } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { JwtService } from '@midwayjs/jwt';
import { UnauthorizedError } from '@midwayjs/core/dist/error/http';
import { JwtAuthConfig, JwtTokenPayload } from '../interface';

/**
 * JWT认证中间件
 */
@Middleware()
export class JwtAuthMiddleware implements IMiddleware<Context, NextFunction> {
  @Inject()
  jwtService: JwtService;

  @Config('jwtAuth')
  jwtAuthConfig: JwtAuthConfig;

  @Config('jwt')
  jwtConfig;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 白名单路径直接放行
      if (this.ignore(ctx)) {
        return await next();
      }

      // 获取 token
      const token = this.getTokenFromHeader(ctx);
      if (!token) {
        throw new UnauthorizedError('未登录或登录已过期');
      }

      try {
        // 验证 token
        const decoded = (await this.jwtService.verify(
          token
        )) as unknown as JwtTokenPayload;
        // 将用户信息挂载到 ctx.state 上
        ctx.state.user = decoded;
        console.log('JWT 验证成功，用户信息:', decoded);

        // 检查是否需要刷新 token
        if (this.shouldRefreshToken(decoded.exp)) {
          // 确保只复制需要的字段，避免类型错误
          const newToken = this.jwtService.signSync(
            {
              userId: decoded.userId,
              username: decoded.username,
              roles: decoded.roles,
              type: decoded.type,
              userCode: decoded.userCode,
              enterpriseCode: decoded.enterpriseCode,
              // 添加过期时间，使用与原 token 相同的过期时间策略
              iat: Math.floor(Date.now() / 1000),
            },
            { expiresIn: this.jwtConfig.expiresIn || '2h' }
          );

          // 在响应头中返回新token
          ctx.set('New-Token', newToken);
          // 同时设置 Authorization 头，确保当前请求后续处理使用新 token
          ctx.set('Authorization', `Bearer ${newToken}`);

          // 记录 token 刷新日志，便于调试
          console.log(
            'Token 已刷新，原过期时间:',
            new Date(decoded.exp * 1000).toISOString()
          );
          console.log('新 token 生成时间:', new Date().toISOString());
        }
      } catch (error) {
        console.error('JWT 验证失败:', error);
        throw new UnauthorizedError('未登录或登录已过期');
      }

      await next();
    };
  }

  // 判断是否需要刷新 token
  private shouldRefreshToken(exp: number): boolean {
    const now = Math.floor(Date.now() / 1000);
    const threshold = this.jwtAuthConfig.refreshThreshold || 30 * 60; // 默认过期前30分钟刷新
    const timeLeft = exp - now;
    console.log(`Token 剩余有效期: ${timeLeft} 秒, 阈值: ${threshold} 秒`);
    return timeLeft > 0 && timeLeft < threshold;
  }

  ignore(ctx: Context): boolean {
    const whitelist = this.jwtAuthConfig.whitelist || [
      '/auth/login',
      '/auth/register',
      '/auth/refresh-token',
      '/public',
      '/weapp',
      '/openapi',
      '/dev-tools',
      '/wepay',
    ];
    return whitelist.some(path => ctx.path.startsWith(path));
  }

  private getTokenFromHeader(ctx: Context): string | null {
    const authorization = ctx.get('Authorization');
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return null;
    }
    return authorization.substring(7);
  }

  static getName(): string {
    return 'JWT_AUTH';
  }
}
