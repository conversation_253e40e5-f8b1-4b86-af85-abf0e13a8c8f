# 问卷创建与管理模块

## 概述

本模块实现了教师评价问卷系统的问卷创建与管理功能，使用 Midway + TypeScript + Sequelize 开发。

## 功能特性

- ✅ 问卷创建与管理
- ✅ SSO 学校ID验证
- ✅ 5/10星级模式支持
- ✅ 问卷状态管理（草稿/已发布/已关闭）
- ✅ 分页查询和筛选
- ✅ 输入验证和错误处理
- ✅ 一对多关联关系（问卷-响应）

## 数据模型

### 问卷实体 (Questionnaire)

```typescript
{
  id: number;                    // 问卷ID
  title: string;                 // 问卷标题
  description?: string;          // 问卷描述
  month: string;                 // 问卷月份 (YYYY-MM)
  status: QuestionnaireStatus;   // 问卷状态
  star_mode: StarMode;           // 星级模式 (5/10)
  sso_school_code: string;         // SSO学校ID
  sso_school_name?: string;      // SSO学校名称
  creator_user_id: string;       // 创建用户ID
  creator_user_name?: string;    // 创建用户名称
  // ... 其他字段
}
```

### 状态枚举

```typescript
enum QuestionnaireStatus {
  DRAFT = 'draft',         // 草稿
  PUBLISHED = 'published', // 已发布
  CLOSED = 'closed'        // 已关闭
}

enum StarMode {
  FIVE_STAR = 5,   // 5星制
  TEN_STAR = 10    // 10星制
}
```

## API 接口

### 1. 创建问卷

**POST** `/api/questionnaire`

**请求体：**
```json
{
  "title": "2024年1月教师评价问卷",
  "description": "本月教师评价问卷",
  "month": "2024-01",
  "sso_school_code": "school_001",
  "star_mode": 5,
  "include_school_evaluation": true,
  "instructions": "请认真填写评价",
  "allow_anonymous": false,
  "max_teachers_limit": 10,
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-31T23:59:59Z"
}
```

**响应：**
```json
{
  "errCode": 0,
  "msg": "问卷创建成功",
  "data": {
    "id": 1,
    "title": "2024年1月教师评价问卷",
    // ... 其他字段
  }
}
```

### 2. 获取问卷列表

**GET** `/api/questionnaire`

**查询参数：**
- `sso_school_code`: 学校ID（可选）
- `month`: 月份 YYYY-MM（可选）
- `status`: 问卷状态（可选）
- `page`: 页码，默认1
- `limit`: 每页数量，默认10

**响应：**
```json
{
  "errCode": 0,
  "msg": "获取问卷列表成功",
  "data": {
    "list": [...],
    "total": 100,
    "page": 1,
    "limit": 10
  }
}
```

### 3. 获取问卷详情

**GET** `/api/questionnaire/:id`

### 4. 更新问卷状态

**PUT** `/api/questionnaire/:id/status`

**请求体：**
```json
{
  "status": "published"
}
```

### 5. 删除问卷

**DELETE** `/api/questionnaire/:id`

注意：只有草稿状态的问卷才能删除。

## 业务规则

### 状态转换规则

- `draft` → `published` ✅
- `draft` → `closed` ✅
- `published` → `closed` ✅
- `closed` → 任何状态 ❌

### 验证规则

1. **学校唯一性**：同一学校同一月份只能有一个问卷
2. **SSO验证**：创建问卷时必须验证学校ID的有效性
3. **时间验证**：开始时间必须早于结束时间
4. **权限验证**：需要JWT认证

## 使用示例

### 服务层使用

```typescript
import { Inject } from '@midwayjs/core';
import { QuestionnaireService } from '../service/questionnaire.service';

@Controller('/api/test')
export class TestController {
  @Inject()
  questionnaireService: QuestionnaireService;

  @Post('/create')
  async createQuestionnaire() {
    const createDto = {
      title: '测试问卷',
      month: '2024-01',
      sso_school_code: 'school_001',
      star_mode: 5
    };
    
    const questionnaire = await this.questionnaireService.createQuestionnaire(
      createDto,
      'user_001',
      '张三'
    );
    
    return questionnaire;
  }
}
```

## 错误处理

所有接口都使用统一的错误响应格式：

```json
{
  "errCode": 400,
  "msg": "错误信息",
  "data": null
}
```

常见错误码：
- `400`: 业务错误
- `401`: 认证错误
- `422`: 参数验证错误
- `500`: 系统错误

## 测试

运行测试：
```bash
npm test
```

测试文件位置：`test/questionnaire.test.ts`

## 注意事项

1. 确保数据库配置正确，特别是 `repositoryMode: true`
2. SSO接口需要正确配置 `apiManagerBaseURL`
3. JWT认证中间件需要正确配置
4. 实体关联关系已配置，支持级联查询
