/**
 * 用户服务参数接口
 * @description User-Service parameters
 */
export interface IUserOptions {
  /** 用户ID */
  uid: number;
}

/**
 * 问卷查询参数接口
 * @description 用于问卷列表查询的参数定义
 */
export interface IQuestionnaireQuery {
  /** SSO学校编码，可选 */
  sso_school_code?: string;
  /** 月份，格式：YYYY-MM，可选 */
  month?: string;
  /** 问卷状态，可选 */
  status?: string;
  /** 页码，默认1，可选 */
  page?: number;
  /** 每页数量，默认10，可选 */
  limit?: number;
}

/**
 * 问卷列表响应接口
 * @description 问卷列表查询的响应数据结构
 */
export interface IQuestionnaireListResponse {
  /** 问卷列表数据 */
  list: any[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
}

/**
 * SSO学校信息接口
 * @description 从SSO系统获取的学校信息
 */
export interface ISSoSchoolInfo {
  /** 学校编码 */
  code: string;
  /** 学校名称 */
  name: string;
  /** 学校状态（active/inactive） */
  status: string;
}

/**
 * SSO学生信息接口
 * @description 从SSO系统获取的学生信息
 */
export interface ISSoStudentInfo {
  /** 学生ID */
  id: string;
  /** 学生姓名 */
  name: string;
  /** 学生班级 */
  class: string;
  /** 学生年级 */
  grade: string;
  /** 学生状态（active/inactive） */
  status: string;
  /** 所属学校编码 */
  school_code: string;
}

/**
 * SSO教师信息接口
 * @description 从SSO系统获取的教师信息
 */
export interface ISSoTeacherInfo {
  /** 教师ID */
  id: string;
  /** 教师姓名 */
  name: string;
  /** 教师科目 */
  subject: string;
  /** 教师职位 */
  position: string;
  /** 教师部门 */
  department: string;
  /** 教师状态（active/inactive） */
  status: string;
  /** 所属学校编码 */
  school_code: string;
}

/**
 * 响应列表响应接口
 * @description 问卷响应列表查询的响应数据结构
 */
export interface IResponseListResponse {
  /** 响应列表数据 */
  list: any[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
}

/**
 * 响应统计信息接口
 * @description 问卷响应的统计数据
 */
export interface IResponseStatistics {
  /** 问卷ID */
  questionnaire_id: number;
  /** 总响应数 */
  total_responses: number;
  /** 已完成响应数 */
  completed_responses: number;
  /** 完成率（百分比） */
  completion_rate: number;
  /** 平均评分 */
  average_rating: number;
  /** 教师评价数量 */
  teacher_evaluation_count: number;
}

/**
 * 提交响应结果接口
 * @description 问卷响应提交成功后的返回数据
 */
export interface ISubmitResponseResult {
  /** 响应ID */
  response_id: number;
  /** 问卷ID */
  questionnaire_id: number;
  /** 提交时间 */
  submission_time: Date;
  /** 总平均分（百分制） */
  total_average_score: number;
  /** 评价教师数量 */
  teacher_count: number;
}

/**
 * 评分转换接口
 * @description 星级评分与百分制分数的转换关系
 */
export interface IRatingConversion {
  /** 星级（1-5或1-10） */
  star: number;
  /** 对应分数（百分制） */
  score: number;
  /** 评分描述 */
  description: string;
}

/**
 * 评分信息接口
 * @description 包含5星制和10星制的评分转换信息
 */
export interface IRatingInfo {
  /** 5星制评分信息 */
  five_star_mode: {
    /** 评分模式描述 */
    description: string;
    /** 转换规则说明 */
    conversion: string;
    /** 评分等级列表 */
    ratings: IRatingConversion[];
  };
  /** 10星制评分信息 */
  ten_star_mode: {
    /** 评分模式描述 */
    description: string;
    /** 转换规则说明 */
    conversion: string;
    /** 评分等级列表 */
    ratings: IRatingConversion[];
  };
}

/**
 * 学校统计数据接口
 * @description 学校维度的统计分析数据
 */
export interface ISchoolStatistics {
  /** SSO学校编码 */
  sso_school_code: string;
  /** SSO学校名称，可选 */
  sso_school_name?: string;
  /** 统计月份，格式：YYYY-MM，可选 */
  month?: string;
  /** 总响应数 */
  total_responses: number;
  /** 已完成响应数 */
  completed_responses: number;
  /** 完成率（百分比） */
  completion_rate: number;
  /** 学校平均分（百分制） */
  school_average_score: number;
  /** 教师平均分（百分制） */
  teacher_average_score: number;
  /** 被评价教师总数 */
  total_teachers_evaluated: number;
  /** 响应趋势数据，可选 */
  response_trend?: ITrendData[];
  /** 教师排名数据，可选 */
  teacher_ranking?: ITeacherRanking[];
}

/**
 * 教师统计数据接口
 * @description 教师维度的统计分析数据
 */
export interface ITeacherStatistics {
  /** SSO教师ID */
  sso_teacher_id: string;
  /** SSO教师姓名，可选 */
  sso_teacher_name?: string;
  /** SSO教师科目，可选 */
  sso_teacher_subject?: string;
  /** SSO教师部门，可选 */
  sso_teacher_department?: string;
  /** 统计月份，格式：YYYY-MM，可选 */
  month?: string;
  /** 总评价数 */
  total_evaluations: number;
  /** 平均分（百分制） */
  average_score: number;
  /** 推荐率（百分比） */
  recommendation_rate: number;
  /** 评分分布数据，可选 */
  score_distribution?: IScoreDistribution[];
  /** 关键词云数据，可选 */
  keyword_cloud?: IKeywordData[];
  /** 评价趋势数据，可选 */
  evaluation_trend?: ITrendData[];
  /** 详细评分数据，可选 */
  detailed_scores?: IDetailedScores;
}

/**
 * 教师排名接口
 * @description 教师评分排名数据
 */
export interface ITeacherRanking {
  /** SSO教师ID */
  sso_teacher_id: string;
  /** SSO教师姓名 */
  sso_teacher_name: string;
  /** SSO教师科目 */
  sso_teacher_subject: string;
  /** SSO教师部门 */
  sso_teacher_department: string;
  /** 平均分（百分制） */
  average_score: number;
  /** 评价数量 */
  evaluation_count: number;
  /** 推荐率（百分比） */
  recommendation_rate: number;
  /** 排名 */
  rank: number;
}

/**
 * 趋势数据接口
 * @description 时间序列的趋势分析数据
 */
export interface ITrendData {
  /** 月份，格式：YYYY-MM */
  month: string;
  /** 总响应数，可选 */
  total_responses?: number;
  /** 已完成响应数，可选 */
  completed_responses?: number;
  /** 完成率（百分比），可选 */
  completion_rate?: number;
  /** 学校平均分，可选 */
  avg_school_score?: number;
  /** 教师平均分，可选 */
  avg_teacher_score?: number;
  /** 评价数量，可选 */
  evaluation_count?: number;
  /** 平均分，可选 */
  average_score?: number;
  /** 推荐率，可选 */
  recommendation_rate?: number;
}

/**
 * 评分分布接口
 * @description 评分区间的分布统计
 */
export interface IScoreDistribution {
  /** 分数区间（如：90-100、80-89等） */
  score_range: string;
  /** 该区间的数量 */
  count: number;
  /** 该区间的占比（百分比） */
  percentage: number;
}

/**
 * 关键词数据接口
 * @description 关键词云的词汇统计
 */
export interface IKeywordData {
  /** 关键词 */
  word: string;
  /** 出现次数 */
  count: number;
}

/**
 * 详细评分接口
 * @description 教师各维度的详细评分
 */
export interface IDetailedScores {
  /** 教学质量评分（百分制） */
  teaching_quality: number;
  /** 教学态度评分（百分制） */
  teaching_attitude: number;
  /** 课堂管理评分（百分制） */
  classroom_management: number;
  /** 沟通能力评分（百分制） */
  communication: number;
  /** 专业知识评分（百分制） */
  professional_knowledge: number;
}

/**
 * 教师排名响应接口
 * @description 教师排名查询的响应数据结构
 */
export interface ITeacherRankingResponse {
  /** 教师排名列表 */
  list: ITeacherRanking[];
  /** 总记录数 */
  total: number;
}

/**
 * 操作日志列表响应接口
 * @description 操作日志列表查询的响应数据结构
 */
export interface IOperationLogListResponse {
  /** 操作日志列表数据 */
  list: any[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
}

/**
 * 操作日志统计接口
 * @description 操作日志的统计分析数据
 */
export interface IOperationLogStatistics {
  /** 总操作数 */
  total_operations: number;
  /** 成功操作数 */
  success_operations: number;
  /** 失败操作数 */
  failed_operations: number;
  /** 成功率（百分比） */
  success_rate: number;
  /** 平均响应时间（毫秒） */
  avg_response_time: number;
  /** 操作趋势数据 */
  operation_trend: any[];
  /** 模块分布数据 */
  module_distribution: any[];
  /** 用户活跃度数据 */
  user_activity: any[];
}
