import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 教师评价DTO
 */
export class TeacherEvaluationDTO {
  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO教师ID不能为空',
      'string.max': 'SSO教师ID不能超过50个字符',
      'any.required': 'SSO教师ID是必填项',
    })
  )
  sso_teacher_id: string;

  @Rule(
    RuleType.number().integer().min(1).max(10).required().messages({
      'number.min': '评分不能小于1',
      'number.max': '评分不能大于10',
      'number.integer': '评分必须是整数',
      'any.required': '评分是必填项',
    })
  )
  rating: number;

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '评价描述不能超过1000个字符',
    })
  )
  description?: string;

  @Rule(
    RuleType.array().items(RuleType.string()).optional().messages({
      'array.base': '评价标签必须是数组格式',
    })
  )
  tags?: string[];

  @Rule(RuleType.boolean().optional())
  is_recommended?: boolean;

  @Rule(
    RuleType.number().integer().min(1).max(10).optional().messages({
      'number.min': '教学质量评分不能小于1',
      'number.max': '教学质量评分不能大于10',
      'number.integer': '教学质量评分必须是整数',
    })
  )
  teaching_quality_rating?: number;

  @Rule(
    RuleType.number().integer().min(1).max(10).optional().messages({
      'number.min': '教学态度评分不能小于1',
      'number.max': '教学态度评分不能大于10',
      'number.integer': '教学态度评分必须是整数',
    })
  )
  teaching_attitude_rating?: number;

  @Rule(
    RuleType.number().integer().min(1).max(10).optional().messages({
      'number.min': '课堂管理评分不能小于1',
      'number.max': '课堂管理评分不能大于10',
      'number.integer': '课堂管理评分必须是整数',
    })
  )
  classroom_management_rating?: number;

  @Rule(
    RuleType.number().integer().min(1).max(10).optional().messages({
      'number.min': '沟通能力评分不能小于1',
      'number.max': '沟通能力评分不能大于10',
      'number.integer': '沟通能力评分必须是整数',
    })
  )
  communication_rating?: number;

  @Rule(
    RuleType.number().integer().min(1).max(10).optional().messages({
      'number.min': '专业知识评分不能小于1',
      'number.max': '专业知识评分不能大于10',
      'number.integer': '专业知识评分必须是整数',
    })
  )
  professional_knowledge_rating?: number;

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '改进建议不能超过1000个字符',
    })
  )
  improvement_suggestions?: string;

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '最满意的方面不能超过1000个字符',
    })
  )
  most_satisfied_aspect?: string;

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '需要改进的方面不能超过1000个字符',
    })
  )
  needs_improvement_aspect?: string;
}

/**
 * 提交响应DTO
 */
export class SubmitResponseDTO {
  @Rule(
    RuleType.number().integer().required().messages({
      'number.integer': '问卷ID必须是整数',
      'any.required': '问卷ID是必填项',
    })
  )
  questionnaire_id: number;

  @Rule(
    RuleType.string()
      .required()
      .pattern(/^1[3-9]\d{9}$/)
      .messages({
        'string.empty': '家长手机号不能为空',
        'string.pattern.base': '家长手机号格式不正确',
        'any.required': '家长手机号是必填项',
      })
  )
  parent_phone: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': '家长姓名不能超过50个字符',
    })
  )
  parent_name?: string;

  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO学生ID不能为空',
      'string.max': 'SSO学生ID不能超过50个字符',
      'any.required': 'SSO学生ID是必填项',
    })
  )
  sso_student_id: string;

  @Rule(
    RuleType.string()
      .required()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.empty': '月份不能为空',
        'string.pattern.base': '月份格式必须为YYYY-MM',
        'any.required': '月份是必填项',
      })
  )
  month: string;

  @Rule(
    RuleType.number().integer().min(1).max(10).optional().messages({
      'number.min': '学校评分不能小于1',
      'number.max': '学校评分不能大于10',
      'number.integer': '学校评分必须是整数',
    })
  )
  school_rating?: number;

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '学校评价描述不能超过1000个字符',
    })
  )
  school_description?: string;

  @Rule(
    RuleType.array()
      .items(
        RuleType.object().keys({
          sso_teacher_id: RuleType.string().required(),
          rating: RuleType.number().integer().min(1).max(10).required(),
          description: RuleType.string().optional(),
          tags: RuleType.array().items(RuleType.string()).optional(),
          is_recommended: RuleType.boolean().optional(),
          teaching_quality_rating: RuleType.number()
            .integer()
            .min(1)
            .max(10)
            .optional(),
          teaching_attitude_rating: RuleType.number()
            .integer()
            .min(1)
            .max(10)
            .optional(),
          classroom_management_rating: RuleType.number()
            .integer()
            .min(1)
            .max(10)
            .optional(),
          communication_rating: RuleType.number()
            .integer()
            .min(1)
            .max(10)
            .optional(),
          professional_knowledge_rating: RuleType.number()
            .integer()
            .min(1)
            .max(10)
            .optional(),
          improvement_suggestions: RuleType.string().optional(),
          most_satisfied_aspect: RuleType.string().optional(),
          needs_improvement_aspect: RuleType.string().optional(),
        })
      )
      .min(1)
      .required()
      .messages({
        'array.min': '至少需要评价一位教师',
        'any.required': '教师评价是必填项',
      })
  )
  teacher_evaluations: TeacherEvaluationDTO[];

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '备注信息不能超过1000个字符',
    })
  )
  remarks?: string;
}

/**
 * 查询响应列表DTO
 */
export class QueryResponseDTO {
  @Rule(
    RuleType.number().integer().optional().messages({
      'number.integer': '问卷ID必须是整数',
    })
  )
  questionnaire_id?: number;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^1[3-9]\d{9}$/)
      .messages({
        'string.pattern.base': '家长手机号格式不正确',
      })
  )
  parent_phone?: string;

  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': 'SSO学生ID不能超过50个字符',
    })
  )
  sso_student_id?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '月份格式必须为YYYY-MM',
      })
  )
  month?: string;

  @Rule(RuleType.boolean().optional())
  is_completed?: boolean;

  @Rule(
    RuleType.number().integer().min(1).default(1).messages({
      'number.min': '页码不能小于1',
      'number.integer': '页码必须是整数',
    })
  )
  page?: number;

  @Rule(
    RuleType.number().integer().min(1).max(100).default(10).messages({
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100',
      'number.integer': '每页数量必须是整数',
    })
  )
  limit?: number;
}
