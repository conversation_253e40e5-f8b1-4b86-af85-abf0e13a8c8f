# 🚀 教师评价问卷系统 - API接口清单

## 📋 接口总览

| 序号 | 模块 | 接口名称 | 方法 | 路径 | 功能描述 |
|------|------|----------|------|------|----------|
| 1 | 问卷管理 | 创建问卷 | POST | `/api/questionnaire` | 创建新的问卷 |
| 2 | 问卷管理 | 获取问卷列表 | GET | `/api/questionnaire` | 分页获取问卷列表 |
| 3 | 问卷管理 | 获取问卷详情 | GET | `/api/questionnaire/:id` | 根据ID获取问卷详情 |
| 4 | 问卷管理 | 更新问卷状态 | PUT | `/api/questionnaire/:id/status` | 更新问卷状态（发布/关闭） |
| 5 | 问卷管理 | 删除问卷 | DELETE | `/api/questionnaire/:id` | 删除问卷 |
| 6 | 问卷填写 | 提交问卷响应 | POST | `/api/response` | 家长提交教师评价 |
| 7 | 问卷填写 | 获取响应列表 | GET | `/api/response` | 分页获取响应列表 |
| 8 | 问卷填写 | 获取响应详情 | GET | `/api/response/:id` | 根据ID获取响应详情 |
| 9 | 问卷填写 | 检查重复提交 | GET | `/api/response/check` | 检查是否已提交响应 |
| 10 | 问卷填写 | 获取问卷统计 | GET | `/api/response/statistics/:id` | 获取问卷统计信息 |
| 11 | 问卷填写 | 获取评分信息 | GET | `/api/response/rating-info/:id` | 获取星级评分转换信息 |
| 12 | 学校统计 | 学校统计概览 | GET | `/api/statistics/school` | 获取学校整体统计数据 |
| 13 | 学校统计 | 学校响应趋势 | GET | `/api/statistics/school/:schoolId/trend` | 获取学校响应趋势数据 |
| 14 | 学校统计 | 教师排名 | GET | `/api/statistics/teacher-ranking` | 获取教师评分排名 |
| 15 | 学校统计 | 趋势分析 | GET | `/api/statistics/trend` | 获取趋势分析数据 |
| 16 | 教师统计 | 教师统计概览 | GET | `/api/statistics/teacher` | 获取教师个人统计数据 |
| 17 | 教师统计 | 教师评分分布 | GET | `/api/statistics/teacher/:teacherId/distribution` | 获取教师评分分布 |
| 18 | 教师统计 | 教师关键词云 | GET | `/api/statistics/teacher/:teacherId/keywords` | 获取教师评价关键词 |
| 19 | 教师统计 | 教师评价趋势 | GET | `/api/statistics/teacher/:teacherId/trend` | 获取教师评价趋势 |
| 20 | 操作日志 | 创建操作日志 | POST | `/api/operation-log` | 手动创建操作日志 |
| 21 | 操作日志 | 获取操作日志列表 | GET | `/api/operation-log` | 分页获取操作日志列表 |
| 22 | 操作日志 | 获取操作日志详情 | GET | `/api/operation-log/:id` | 根据ID获取操作日志详情 |
| 23 | 操作日志 | 获取操作日志统计 | GET | `/api/operation-log/statistics` | 获取操作日志统计信息 |
| 24 | 操作日志 | 获取用户操作历史 | GET | `/api/operation-log/user/:userId/history` | 获取指定用户的操作历史 |
| 25 | 操作日志 | 获取学校操作日志 | GET | `/api/operation-log/school/:schoolId` | 获取指定学校的操作日志 |
| 26 | 操作日志 | 获取操作日志趋势 | GET | `/api/operation-log/trend` | 获取操作日志趋势分析 |
| 27 | 操作日志 | 清理过期日志 | POST | `/api/operation-log/cleanup` | 清理过期的操作日志 |

## 🔧 快速测试

### 基础配置
- **Base URL**: `http://localhost:3141`
- **Content-Type**: `application/json`

### 常用接口示例

#### 1. 创建问卷
```bash
curl -X POST http://localhost:3141/api/questionnaire \
  -H "Content-Type: application/json" \
  -d '{
    "title": "2024年1月教师评价问卷",
    "month": "2024-01",
    "sso_school_code": "school_001",
    "star_mode": 5
  }'
```

#### 2. 提交问卷响应
```bash
curl -X POST http://localhost:3141/api/response \
  -H "Content-Type: application/json" \
  -d '{
    "questionnaire_id": 1,
    "parent_phone": "13800138000",
    "sso_student_id": "student_001",
    "month": "2024-01",
    "teacher_evaluations": [
      {
        "sso_teacher_id": "teacher_001",
        "rating": 5
      }
    ]
  }'
```

#### 3. 获取学校统计
```bash
curl "http://localhost:3141/api/statistics/school?sso_school_code=school_001&month=2024-01"
```

#### 4. 获取教师排名
```bash
curl "http://localhost:3141/api/statistics/teacher-ranking?sso_school_code=school_001&limit=10"
```

## 📊 响应格式

### 成功响应
```json
{
  "errCode": 0,
  "msg": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "errCode": 400,
  "msg": "错误描述",
  "data": null
}
```

## 🔍 错误码说明

| 错误码 | 说明 | 常见场景 |
|--------|------|----------|
| 0 | 成功 | 操作成功完成 |
| 400 | 业务错误 | 问卷不存在、重复提交、数据验证失败 |
| 401 | 参数错误 | 必填参数缺失、格式错误 |
| 500 | 系统错误 | 数据库连接失败、服务异常 |

## 📝 重要参数说明

### 星级评分转换
- **5星制**: 1星=20分, 2星=40分, 3星=60分, 4星=80分, 5星=100分
- **10星制**: 1星=10分, 2星=20分, ..., 10星=100分

### 月份格式
- 格式: `YYYY-MM`
- 示例: `2024-01`, `2024-12`

### 分页参数
- `page`: 页码，从1开始
- `limit`: 每页数量，默认10，最大100

### 排序参数
- `sort_by`: 排序字段
- `sort_order`: 排序方向（ASC/DESC）

## 🧪 测试状态

| 接口 | 测试状态 | 备注 |
|------|----------|------|
| 问卷管理模块 | ✅ 已测试 | 5个接口全部通过 |
| 问卷填写模块 | ✅ 已测试 | 6个接口全部通过 |
| 统计分析模块 | ✅ 已测试 | 8个接口全部通过 |
| 操作日志模块 | ✅ 已测试 | 8个接口全部通过 |

**总计**: 27个接口，全部测试通过 ✅

## 📚 详细文档

- [📡 完整API参考文档](docs/api-reference.md)
- [🗂️ 问卷管理模块](docs/questionnaire-module.md)
- [📝 问卷填写模块](docs/response-module.md)
- [📊 统计分析模块](docs/statistics-module.md)

---

**🎉 教师评价问卷系统 - 27个API接口，功能完整，测试通过！**
