import { Rule, RuleType } from '@midwayjs/validate';
import { QuestionnaireStatus, StarMode } from '../entity/questionnaire.entity';

/**
 * 创建问卷DTO
 */
export class CreateQuestionnaireDTO {
  @Rule(
    RuleType.string().required().max(200).messages({
      'string.empty': '问卷标题不能为空',
      'string.max': '问卷标题不能超过200个字符',
      'any.required': '问卷标题是必填项',
    })
  )
  title: string;

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '问卷描述不能超过1000个字符',
    })
  )
  description?: string;

  @Rule(
    RuleType.string()
      .required()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.empty': '问卷月份不能为空',
        'string.pattern.base': '问卷月份格式必须为YYYY-MM',
        'any.required': '问卷月份是必填项',
      })
  )
  month: string;

  @Rule(
    RuleType.string().required().max(50).messages({
      'string.empty': 'SSO学校编码不能为空',
      'string.max': 'SSO学校编码不能超过50个字符',
      'any.required': 'SSO学校编码是必填项',
    })
  )
  sso_school_code: string;

  @Rule(
    RuleType.number()
      .valid(StarMode.FIVE_STAR, StarMode.TEN_STAR)
      .default(StarMode.FIVE_STAR)
      .messages({
        'any.only': '星级模式只能是5或10',
      })
  )
  star_mode?: StarMode;

  @Rule(RuleType.boolean().default(true))
  include_school_evaluation?: boolean;

  @Rule(
    RuleType.string().optional().max(1000).messages({
      'string.max': '问卷说明不能超过1000个字符',
    })
  )
  instructions?: string;

  @Rule(RuleType.boolean().default(false))
  allow_anonymous?: boolean;

  @Rule(
    RuleType.number().integer().min(0).default(0).messages({
      'number.min': '最大评价教师数量不能小于0',
      'number.integer': '最大评价教师数量必须是整数',
    })
  )
  max_teachers_limit?: number;

  @Rule(
    RuleType.date().optional().messages({
      'date.base': '问卷开始时间格式不正确',
    })
  )
  start_time?: Date;

  @Rule(
    RuleType.date().optional().messages({
      'date.base': '问卷结束时间格式不正确',
    })
  )
  end_time?: Date;
}

/**
 * 更新问卷状态DTO
 */
export class UpdateQuestionnaireStatusDTO {
  @Rule(
    RuleType.string()
      .valid(...Object.values(QuestionnaireStatus))
      .required()
      .messages({
        'any.only': '问卷状态只能是draft、published或closed',
        'any.required': '问卷状态是必填项',
      })
  )
  status: QuestionnaireStatus;
}

/**
 * 查询问卷列表DTO
 */
export class QueryQuestionnaireDTO {
  @Rule(
    RuleType.string().optional().max(50).messages({
      'string.max': 'SSO学校编码不能超过50个字符',
    })
  )
  sso_school_code?: string;

  @Rule(
    RuleType.string()
      .optional()
      .pattern(/^\d{4}-\d{2}$/)
      .messages({
        'string.pattern.base': '月份格式必须为YYYY-MM',
      })
  )
  month?: string;

  @Rule(
    RuleType.string()
      .optional()
      .valid(...Object.values(QuestionnaireStatus))
      .messages({
        'any.only': '问卷状态只能是draft、published或closed',
      })
  )
  status?: QuestionnaireStatus;

  @Rule(
    RuleType.number().integer().min(1).default(1).messages({
      'number.min': '页码不能小于1',
      'number.integer': '页码必须是整数',
    })
  )
  page?: number;

  @Rule(
    RuleType.number().integer().min(1).max(100).default(10).messages({
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100',
      'number.integer': '每页数量必须是整数',
    })
  )
  limit?: number;
}
