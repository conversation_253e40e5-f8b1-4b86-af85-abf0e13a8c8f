/*
 * @Description: 自定义业务异常
 * @Date: 2024-12-27 16:34:38
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2024-12-27 16:45:06
 */

import { MidwayHttpError } from '@midwayjs/core';
import { ErrorCode } from '../common/ErrorCode';

/**
 * 基础业务异常类
 */
export class BusinessError extends MidwayHttpError {
  public errCode: number;

  constructor(msg: string, errCode = ErrorCode.BIZ_ERROR) {
    super(msg, errCode);
    this.errCode = errCode;
  }
}

/**
 * 认证异常
 */
export class AuthError extends BusinessError {
  constructor(msg = '认证失败') {
    super(msg, ErrorCode.AUTH_ERROR);
  }
}

/**
 * 参数校验异常
 */
export class ParamError extends BusinessError {
  constructor(msg = '参数校验失败') {
    super(msg, ErrorCode.PARAM_ERROR);
  }
}

/**
 * 资源不存在异常
 */
export class NotFoundError extends BusinessError {
  constructor(msg = '资源不存在') {
    super(msg, ErrorCode.NOT_FOUND);
  }
}

/**
 * 系统异常
 */
export class SystemError extends BusinessError {
  constructor(msg = '系统错误') {
    super(msg, ErrorCode.SYS_ERROR);
  }
}

// 保持向后兼容
export class CustomError extends BusinessError {
  constructor(msg: string, code = ErrorCode.BIZ_ERROR) {
    super(msg, code);
  }
}
