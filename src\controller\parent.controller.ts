import { Controller, Post, Get, Inject, Body, Query } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { ParentService } from '../service/parent.service';
import { VerifyParentPhoneDTO, StudentQuestionnairesDTO } from '../dto/parent.dto';
import { BaseController } from '../common/BaseController';
import { ApiResponseUtil } from '../common/ApiResponse';

@Controller('/api/parent')
export class ParentController extends BaseController {
  @Inject()
  parentService: ParentService;

  /**
   * 验证家长手机号
   * 通过查询该手机号下的学生信息来验证手机号是否有效
   */
  @Post('/verify-phone')
  @Validate()
  async verifyPhone(@Body() verifyDto: VerifyParentPhoneDTO) {
    this.ctx.logger.info('家长手机号验证请求', {
      phone: verifyDto.phone,
      client_ip: this.ctx.request.ip || 'unknown',
    });

    const result = await this.parentService.verifyParentPhone(verifyDto.phone);

    // 记录验证结果
    this.ctx.logger.info('家长手机号验证完成', {
      phone: verifyDto.phone,
      is_valid: result.is_valid,
      children_count: result.parent?.children?.length || 0,
    });

    const message = result.is_valid ? '手机号验证成功' : '手机号验证失败';
    return ApiResponseUtil.success(result, message);
  }

  /**
   * 查询学生可填写的问卷
   * 根据学校编码、学生ID、月份查询该学生是否有问卷可填写
   */
  @Get('/student-questionnaires')
  @Validate()
  async getStudentQuestionnaires(@Query() queryDto: StudentQuestionnairesDTO) {
    this.ctx.logger.info('查询学生问卷请求', {
      sso_school_code: queryDto.sso_school_code,
      sso_student_id: queryDto.sso_student_id,
      month: queryDto.month,
      parent_phone: queryDto.parent_phone,
      client_ip: this.ctx.request.ip || 'unknown',
    });

    const result = await this.parentService.getStudentQuestionnaires(
      queryDto.sso_school_code,
      queryDto.sso_student_id,
      queryDto.month,
      queryDto.parent_phone
    );

    // 记录查询结果
    this.ctx.logger.info('查询学生问卷完成', {
      sso_school_code: queryDto.sso_school_code,
      sso_student_id: queryDto.sso_student_id,
      month: queryDto.month,
      has_questionnaire: result.has_questionnaire,
      is_submitted: result.questionnaire?.is_submitted || false,
    });

    return ApiResponseUtil.success(result, '查询完成');
  }
}
