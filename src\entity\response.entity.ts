import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Questionnaire } from './questionnaire.entity';
import { Answer } from './answer.entity';

@Table({
  tableName: 'responses',
  comment: '问卷响应表',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'idx_response_questionnaire',
      fields: ['questionnaire_id'],
    },
    {
      name: 'idx_response_phone',
      fields: ['parent_phone'],
    },
    {
      name: 'idx_response_student',
      fields: ['sso_student_id'],
    },
    {
      name: 'idx_response_month',
      fields: ['month'],
    },
    {
      // 唯一键：家长手机号+问卷ID+学生ID+月份
      name: 'uk_response_unique',
      fields: ['parent_phone', 'questionnaire_id', 'sso_student_id', 'month'],
      unique: true,
    },
  ],
})
export class Response extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '响应ID',
  })
  id?: number = undefined;

  @ForeignKey(() => Questionnaire)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '问卷ID',
  })
  questionnaire_id: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    validate: {
      is: /^1[3-9]\d{9}$/, // 中国手机号格式验证
    },
    comment: '家长手机号',
  })
  parent_phone: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '家长姓名',
  })
  parent_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: 'SSO学生ID',
  })
  sso_student_id: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO学生姓名',
  })
  sso_student_name: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: 'SSO学生班级',
  })
  sso_student_class: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: 'SSO学生年级',
  })
  sso_student_grade: string;

  @Column({
    type: DataType.STRING(7),
    allowNull: false,
    comment: '月份（YYYY-MM格式）',
  })
  month: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 10, // 最大支持10星制
    },
    comment: '学校评分（如果问卷包含学校评价）',
  })
  school_rating: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '学校评价描述',
  })
  school_description: string;

  @Column({
    type: DataType.DECIMAL(5, 2),
    allowNull: true,
    comment: '总平均分（所有教师评分的平均值，百分制）',
  })
  total_average_score: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '评价教师数量',
  })
  teacher_count: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '评价完成状态',
  })
  is_completed: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '提交时间',
  })
  submitted_at: Date;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '手机验证状态',
  })
  phone_verified: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '手机验证时间',
  })
  phone_verified_at: Date;

  @Column({
    type: DataType.STRING(45),
    allowNull: true,
    comment: 'IP地址',
  })
  ip_address: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '用户代理',
  })
  user_agent: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '备注信息',
  })
  remarks: string;

  @CreatedAt
  created_at: Date;

  @UpdatedAt
  updated_at: Date;

  // 关联关系
  @BelongsTo(() => Questionnaire)
  questionnaire: Questionnaire;

  @HasMany(() => Answer)
  answers: Answer[];
}
