/**
 * 统一API响应格式
 */
export interface ApiResponse<T = any> {
  errCode: number;
  msg: string;
  data: T;
  timestamp?: number;
}

/**
 * 分页响应数据格式
 */
export interface PageData<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * API响应工具类
 */
export class ApiResponseUtil {
  /**
   * 成功响应
   */
  static success<T>(data: T, msg = '操作成功'): ApiResponse<T> {
    return {
      errCode: 0,
      msg,
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * 失败响应
   */
  static error(errCode: number, msg: string, data: any = null): ApiResponse {
    return {
      errCode,
      msg,
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * 分页成功响应
   */
  static successPage<T>(
    list: T[],
    total: number,
    page: number,
    pageSize: number,
    msg = '获取数据成功'
  ): ApiResponse<PageData<T>> {
    return this.success(
      {
        list,
        total,
        page,
        pageSize,
      },
      msg
    );
  }
}
