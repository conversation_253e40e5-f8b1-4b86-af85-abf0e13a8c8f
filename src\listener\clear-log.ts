import { Provide, Scope, ScopeEnum, Inject } from '@midwayjs/core';
import { DataListener } from '@midwayjs/core';
import { OperationLogService } from '../service/operation-log.service';

@Provide()
@Scope(ScopeEnum.Singleton)
export class ClearLogListener extends DataListener<{
  lastCleanupTime: Date | null;
  nextCleanupTime: Date | null;
}> {
  @Inject()
  operationLogService: OperationLogService;

  private cleanupTimer: NodeJS.Timeout | null = null;
  private lastCleanupTime: Date | null = null;

  // 初始化数据
  initData() {
    console.log('日志清理监听器：初始化');
    const nextCleanupTime = this.calculateNextCleanupTime();
    console.log(
      `日志清理监听器：下次清理时间 ${nextCleanupTime.toLocaleString()}`
    );

    return {
      lastCleanupTime: this.lastCleanupTime,
      nextCleanupTime: nextCleanupTime,
    };
  }

  // 更新数据
  onData(
    setData: (data: {
      lastCleanupTime: Date | null;
      nextCleanupTime: Date | null;
    }) => void
  ) {
    console.log('日志清理监听器：启动每日清理任务');

    // 计算到下次执行时间的延迟
    const nextCleanupTime = this.calculateNextCleanupTime();
    const now = new Date();
    const delay = nextCleanupTime.getTime() - now.getTime();

    console.log(
      `日志清理监听器：将在 ${Math.round(delay / 1000 / 60)} 分钟后执行首次清理`
    );

    // 设置首次执行的延迟定时器
    setTimeout(
      () => {
        // 执行首次清理
        this.executeCleanup(setData);

        // 设置每日执行的定时器（24小时间隔）
        this.cleanupTimer = setInterval(() => {
          this.executeCleanup(setData);
        }, 24 * 60 * 60 * 1000); // 24小时
      },
      delay > 0 ? delay : 0
    );
  }

  /**
   * 执行清理任务
   */
  private async executeCleanup(
    setData: (data: {
      lastCleanupTime: Date | null;
      nextCleanupTime: Date | null;
    }) => void
  ) {
    try {
      console.log('日志清理监听器：开始执行日志清理任务');

      // 清除超过3个月（90天）的日志
      const deletedCount = await this.operationLogService.cleanupExpiredLogs(
        90
      );

      this.lastCleanupTime = new Date();
      const nextCleanupTime = this.calculateNextCleanupTime();

      console.log(
        `日志清理监听器：清理完成，删除了 ${deletedCount} 条超过90天的日志记录`
      );
      console.log(
        `日志清理监听器：下次清理时间 ${nextCleanupTime.toLocaleString()}`
      );

      // 更新状态
      setData({
        lastCleanupTime: this.lastCleanupTime,
        nextCleanupTime: nextCleanupTime,
      });
    } catch (error) {
      console.error('日志清理监听器：清理任务执行失败', error);
    }
  }

  /**
   * 计算下次清理时间（每天凌晨2点）
   */
  private calculateNextCleanupTime(): Date {
    const now = new Date();
    const todayCleanupTime = new Date(now);
    todayCleanupTime.setHours(2, 0, 0, 0); // 设置为凌晨2点

    // 如果今天的清理时间已经过了，则设置为明天
    if (todayCleanupTime <= now) {
      todayCleanupTime.setDate(todayCleanupTime.getDate() + 1);
    }

    return todayCleanupTime;
  }

  /**
   * 清理资源
   */
  async destroyListener() {
    console.log('日志清理监听器：清理资源');

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    console.log('日志清理监听器：资源清理完成');
  }
}
