/**
 * 学校平台用户信息
 */
export interface IUserInfo {
  /** 编码 */
  code: string;
  /** 创建时间 */
  createdAt: string;
  /** 电子邮箱 */
  email: null | string;
  /** 就业状态 */
  employment_status: string;
  /** 企业ID */
  enterpriseId: string;
  /** 人脸识别MD5码 */
  facecodeMD5: null | string;
  /** 性别 */
  gender: null | string;
  /** ID */
  id: string;
  /** 是否托管 */
  isTrusteeship: boolean;
  /** 座机号码 */
  landline: null | string;
  /** 成员类型列表 */
  memberTypes: MemberType[];
  /** 手机号码 */
  mobile: null | string;
  /** 姓名 */
  name: string;
  /** 新编码 */
  newCode: string;
  /** 新工号 */
  newWorkCode: string;
  /** 组织成员列表 */
  organizationMembers: OrganizationMember[];
  /** 职位 */
  position?: string;
  /** 更新时间 */
  updatedAt: string;
  /** 用户名 */
  username: null | string;
  /** 工号 */
  work_code: string;
}

export interface IEnterprise {
  id: string;
  code: string;
  name: string;
  english_name: string | null;
  type_code: string;
  type_name: string;
  region: string;
  region_code: string;
  liasion: string;
  mobile: string;
  address: string;
  social_credit_code: string;
  official_url: string | null;
  email: string;
  fax: string | null;
  postal_code: string;
  service_scope: string | null;
  legal_person: string | null;
  legal_person_code: string | null;
  section_name: string;
  section_alias_name: string;
  section_code: string;
  describe: string;
  school_motto: string;
  remark: string | null;
  honor: string;
  theme: string;
  style: string | null;
  logo_url: string;
  business_license_url: string;
  school_license_url: string;
  independent: boolean;
  hosted: boolean;
  hostedTime: string | null;
  showInitMessage: boolean;
  need_improve: boolean;
  teacher_need_improve: boolean;
  smart_screen_type: number;
  createdAt: string;
  updatedAt: string;
}

export interface IClass {
  id: string;
  code: string;
  orderIndex: number;
  name: string;
  grade_code: string;
  grade_name: string;
  site_code: string;
  site_name: string;
  monitor: string | null;
  monitorCode: string | null;
  slogan: string;
  graduation: boolean;
  section_name: string;
  section_code: string;
  stage: string;
  graduation_semester: string;
  memberId: string;
  expectation: string;
  member_code: string | null;
  member_name: string | null;
  semesterCode: string;
  semesterName: string;
  enterpriseId: string;
  'class-student': {
    classId: string;
    studentId: string;
    code: string;
  };
  enterprise: IEnterprise;
}

export interface IStudent {
  id: string;
  code: string;
  work_code: string;
  stage: string;
  graduation_semester: string;
  username: string | null;
  name: string;
  avatar: string | null;
  IDNumber: string | null;
  gender: string;
  mobile: string | null;
  email: string;
  graduation: boolean;
  graduationDate: string | null;
  status: string;
  remark: string | null;
  household_register: string | null;
  current_address: string | null;
  enterpriseId: string;
  facecode: string | null;
  facecodeMD5: string | null;
  faceImg: string | null;
  isNeedCard: boolean;
  cardInfo: string | null;
  outCardInfo: string | null;
  isUpdate: boolean;
  createdAt: string;
  updatedAt: string;
  class: IClass;
}

export interface ParentChild {
  id: string;
  relation: string;
  is_guardian: boolean;
  parentId: string;
  studentId: string;
  student: IStudent;
}

export interface ParentInfo {
  id: string;
  mobile: string;
  code: string;
  name: string;
  avatar: string;
  username: string;
  gender: string | null;
  email: string;
  address: string;
  facecode: string | null;
  facecodeMD5: string | null;
  faceImg: string | null;
  createdAt: string;
  updatedAt: string;
  children: ParentChild[];
}
