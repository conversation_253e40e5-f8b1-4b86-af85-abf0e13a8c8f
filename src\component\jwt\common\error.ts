/*
 * @Description: JWT组件错误类
 * @Date: 2025-05-31
 */

import { MidwayHttpError } from '@midwayjs/core';

/**
 * JWT组件错误码
 */
export enum JwtErrorCode {
  OK = 0,
  BIZ_ERROR = 400,
  AUTH_ERROR = 401,
  NOT_FOUND = 404,
  PARAM_ERROR = 422,
  TIME_OUT = 408,
  SYS_ERROR = 500,
}

/**
 * JWT组件基础业务异常类
 */
export class JwtBusinessError extends MidwayHttpError {
  public errCode: number;

  constructor(msg: string, errCode = JwtErrorCode.BIZ_ERROR) {
    super(msg, errCode);
    this.errCode = errCode;
    this.name = 'JwtBusinessError';
  }
}

/**
 * JWT认证异常
 */
export class JwtAuthError extends JwtBusinessError {
  constructor(msg = '认证失败') {
    super(msg, JwtErrorCode.AUTH_ERROR);
    this.name = 'JwtAuthError';
  }
}

/**
 * JWT参数校验异常
 */
export class JwtParamError extends JwtBusinessError {
  constructor(msg = '参数校验失败') {
    super(msg, JwtErrorCode.PARAM_ERROR);
    this.name = 'JwtParamError';
  }
}

/**
 * JWT资源不存在异常
 */
export class JwtNotFoundError extends JwtBusinessError {
  constructor(msg = '资源不存在') {
    super(msg, JwtErrorCode.NOT_FOUND);
    this.name = 'JwtNotFoundError';
  }
}

/**
 * JWT系统异常
 */
export class JwtSystemError extends JwtBusinessError {
  constructor(msg = '系统错误') {
    super(msg, JwtErrorCode.SYS_ERROR);
    this.name = 'JwtSystemError';
  }
}

// 保持向后兼容的通用错误类
export class JwtCustomError extends JwtBusinessError {
  constructor(msg: string, code = JwtErrorCode.BIZ_ERROR) {
    super(msg, code);
    this.name = 'JwtCustomError';
  }
}
